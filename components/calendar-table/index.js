// components/calendar-table/index.js
import Api from "../../utils/api.js";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 仓库ID，null表示全局模式
    warehouseId: {
      type: String,
      value: null,
    },
    // 服装ID，用于服装级别的日志查询
    clothingId: {
      type: String,
      value: null,
    },
    // OEM服装ID，用于OEM服装级别的日志查询
    oemClothingId: {
      type: String,
      value: null,
    },
    // 是否显示查询按钮
    showQueryButton: {
      type: Boolean,
      value: true,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 日历显示相关
    currentYear: new Date().getFullYear(),
    currentMonth: new Date().getMonth() + 1,
    calendarDays: [], // 当月日历数据
    weekDays: ["日", "一", "二", "三", "四", "五", "六"],

    // 日期选择相关
    selectedDates: [], // 选中的日期数组
    startDate: null, // 起始日期

    // 数据相关
    dailyOutboundData: {}, // 每日出库数据 {date: count}
    dailyInboundData: {}, // 每日入库数据 {date: count}
    dataCache: {}, // 数据缓存 {key: data}
    inboundDataCache: {}, // 入库数据缓存 {key: data}

    // UI状态
    showYearPicker: false,
    showMonthPicker: false,
    loading: false, // 保留用于向后兼容
    outboundLoading: false, // 出库数据加载状态
    inboundLoading: false, // 入库数据加载状态

    // 选中日期的总包裹数
    selectedDatesTotal: 0,

    // 年月选择器数据
    yearOptions: [],
    currentYearIndex: 2, // 当前年份在yearOptions中的索引
    monthOptions: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],

    // 手势相关
    touchStartX: 0,
    touchStartY: 0,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 组件初始化
     */
    init() {
      this.generateYearOptions();
      this.generateCalendar();
      this.loadDailyOutboundData();
      this.loadDailyInboundData(); // 加载入库数据
    },

    /**
     * 生成年份选项（近5年）
     */
    generateYearOptions() {
      const currentYear = new Date().getFullYear();
      const yearOptions = [];
      let currentYearIndex = 0;

      for (let i = currentYear - 4; i <= currentYear; i++) {
        yearOptions.push(`${i}年`);
        if (i === this.data.currentYear) {
          currentYearIndex = yearOptions.length - 1;
        }
      }

      this.setData({
        yearOptions,
        currentYearIndex
      });
    },

    /**
     * 生成日历数据
     */
    generateCalendar() {
      const { currentYear, currentMonth } = this.data;

      // 获取当月第一天是星期几
      const firstDay = new Date(currentYear, currentMonth - 1, 1).getDay();

      // 获取当月总天数
      const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();

      // 获取上个月的总天数
      const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;
      const prevYear = currentMonth === 1 ? currentYear - 1 : currentYear;
      const daysInPrevMonth = new Date(prevYear, prevMonth, 0).getDate();

      const calendarDays = [];

      // 添加上个月的日期（灰色显示）
      for (let i = firstDay - 1; i >= 0; i--) {
        calendarDays.push({
          date: daysInPrevMonth - i,
          fullDate: this.formatDate(prevYear, prevMonth, daysInPrevMonth - i),
          isCurrentMonth: false,
          isToday: false,
          isSelected: false,
          outboundCount: 0,
        });
      }

      // 添加当月的日期
      const today = new Date();
      const isCurrentYearMonth =
        currentYear === today.getFullYear() &&
        currentMonth === today.getMonth() + 1;

      for (let date = 1; date <= daysInMonth; date++) {
        const fullDate = this.formatDate(currentYear, currentMonth, date);
        const isToday = isCurrentYearMonth && date === today.getDate();

        calendarDays.push({
          date,
          fullDate,
          isCurrentMonth: true,
          isToday,
          isSelected: false,
          outboundCount: 0,
          hasInbound: false, // 是否有入库记录
        });
      }

      // 添加下个月的日期，补齐6行42个格子
      const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
      const nextYear = currentMonth === 12 ? currentYear + 1 : currentYear;
      const remainingDays = 42 - calendarDays.length;

      for (let date = 1; date <= remainingDays; date++) {
        calendarDays.push({
          date,
          fullDate: this.formatDate(nextYear, nextMonth, date),
          isCurrentMonth: false,
          isToday: false,
          isSelected: false,
          outboundCount: 0,
        });
      }

      this.setData({ calendarDays });
    },

    /**
     * 格式化日期为 YYYY-MM-DD
     */
    formatDate(year, month, date) {
      return `${year}-${String(month).padStart(2, "0")}-${String(date).padStart(
        2,
        "0"
      )}`;
    },

    /**
     * 加载每日出库数据
     */
    async loadDailyOutboundData() {
      // 防止重复请求 - 使用独立的出库loading状态
      if (this.data.outboundLoading) {
        console.log("出库数据正在加载中，跳过重复请求");
        return;
      }

      const { currentYear, currentMonth, warehouseId, clothingId, oemClothingId, dataCache } = this.data;

      // 生成缓存键，包含所有过滤条件
      const cacheKey = `${currentYear}-${currentMonth}-${warehouseId || "all"}-${clothingId || "none"}-${oemClothingId || "none"}`;

      // 检查缓存
      if (dataCache[cacheKey]) {
        console.log("使用缓存数据:", cacheKey);
        this.setData({ dailyOutboundData: dataCache[cacheKey] });
        this.updateCalendarWithOutboundData();
        return;
      }

      try {
        this.setData({
          outboundLoading: true,
          loading: true // 保留用于向后兼容
        });

        const params = {
          year: currentYear,
          month: currentMonth,
        };

        // 如果有仓库ID，添加到参数中
        if (warehouseId) {
          params.warehouse_id = warehouseId;
        }

        // 如果有服装ID，添加到参数中
        if (clothingId) {
          params.clothing_id = clothingId;
        }

        // 如果有OEM服装ID，添加到参数中
        if (oemClothingId) {
          params.oem_clothing_id = oemClothingId;
        }

        console.log("加载每日出库数据，参数:", params);

        const response = await Api.getDailyOutboundSummary(params);

        if (response.data && response.data.code === 200) {
          const dailyOutboundData = response.data.data || {};

          // 更新缓存
          const newDataCache = { ...dataCache };
          newDataCache[cacheKey] = dailyOutboundData;

          this.setData({
            dailyOutboundData,
            dataCache: newDataCache,
          });
          this.updateCalendarWithOutboundData();
          console.log("每日出库数据加载成功:", dailyOutboundData);

          // 验证数据是否正确更新到日历中
          console.log(
            "日历数据更新后的状态:",
            this.data.calendarDays.filter((day) => day.outboundCount > 0)
          );
        } else {
          console.warn("获取每日出库数据响应异常:", response);
          // 设置空数据，避免显示错误
          this.setData({ dailyOutboundData: {} });
          this.updateCalendarWithOutboundData();
        }
      } catch (error) {
        console.error("加载每日出库数据失败:", error);

        // 设置空数据，避免显示错误
        this.setData({ dailyOutboundData: {} });
        this.updateCalendarWithOutboundData();
      } finally {
        this.setData({
          outboundLoading: false,
          loading: false // 保留用于向后兼容
        });
      }
    },

    /**
     * 加载每日入库数据
     */
    async loadDailyInboundData() {
      console.log("开始加载每日入库数据");
      // 防止重复请求 - 使用独立的入库loading状态
      if (this.data.inboundLoading) {
        console.log("入库数据正在加载中，跳过重复请求");
        return;
      }

      const { currentYear, currentMonth, warehouseId, clothingId, oemClothingId, inboundDataCache } = this.data;

      // 生成缓存键，包含所有过滤条件
      const cacheKey = `inbound-${currentYear}-${currentMonth}-${warehouseId || "all"}-${clothingId || "none"}-${oemClothingId || "none"}`;

      // 检查缓存
      if (inboundDataCache[cacheKey]) {
        console.log("使用入库数据缓存:", cacheKey);
        this.setData({ dailyInboundData: inboundDataCache[cacheKey] });
        this.updateCalendarWithInboundData();
        return;
      }

      try {
        // 设置入库数据加载状态
        this.setData({ inboundLoading: true });

        const params = {
          year: currentYear,
          month: currentMonth,
        };

        // 如果有仓库ID，添加到参数中
        if (warehouseId) {
          params.warehouse_id = warehouseId;
        }

        // 如果有服装ID，添加到参数中
        if (clothingId) {
          params.clothing_id = clothingId;
        }

        // 如果有OEM服装ID，添加到参数中
        if (oemClothingId) {
          params.oem_clothing_id = oemClothingId;
        }

        console.log("加载每日入库数据，参数:", params);

        const response = await Api.getDailyInboundSummary(params);
        console.log("API入库响应:", response);
        if (response.data && response.data.code === 200) {
          const dailyInboundData = response.data.data || {};
          console.log("API响应成功，入库数据:", dailyInboundData);
          console.log("入库数据条目数:", Object.keys(dailyInboundData).length);

          // 更新缓存
          const newInboundDataCache = { ...inboundDataCache };
          newInboundDataCache[cacheKey] = dailyInboundData;

          this.setData({
            dailyInboundData,
            inboundDataCache: newInboundDataCache,
          });
          this.updateCalendarWithInboundData();
          console.log("每日入库数据加载成功，已更新日历显示");
        } else {
          console.warn("获取每日入库数据响应异常:", response);
        }
      } catch (error) {
        console.error("加载每日入库数据失败:", error);

        // 设置空数据，避免显示错误
        this.setData({ dailyInboundData: {} });
        this.updateCalendarWithInboundData();
      } finally {
        // 重置入库数据加载状态
        this.setData({ inboundLoading: false });
      }
    },

    /**
     * 更新日历显示出库数据
     */
    updateCalendarWithOutboundData() {
      const { calendarDays, dailyOutboundData } = this.data;

      const updatedCalendarDays = calendarDays.map((day) => {
        const rawCount = dailyOutboundData[day.fullDate] || 0;
        // 确保数值类型正确，支持小数
        const outboundCount =
          typeof rawCount === "number" ? rawCount : parseFloat(rawCount) || 0;

        return {
          ...day,
          outboundCount,
        };
      });

      this.setData({ calendarDays: updatedCalendarDays });

      // 更新选中日期的总数
      this.triggerSelectionUpdate();

      // 调试信息：检查小数数据是否正确处理
      const daysWithOutbound = updatedCalendarDays.filter(
        (day) => day.outboundCount > 0
      );
      if (daysWithOutbound.length > 0) {
        console.log(
          "日历中有出库数据的日期:",
          daysWithOutbound.map((day) => ({
            date: day.fullDate,
            count: day.outboundCount,
            isDecimal: day.outboundCount % 1 !== 0,
          }))
        );
      }
    },

    /**
     * 更新日历中的入库数据显示
     */
    updateCalendarWithInboundData() {
      const { calendarDays, dailyInboundData } = this.data;
      console.log("开始更新日历入库数据显示");
      console.log("当前日历天数:", calendarDays.length);
      console.log("入库数据:", dailyInboundData);

      let hasInboundDays = 0;
      const updatedCalendarDays = calendarDays.map((day) => {
        const inboundCount = dailyInboundData[day.fullDate] || 0;
        const hasInbound = inboundCount > 0;
        if (hasInbound) {
          hasInboundDays++;
          console.log(`日期 ${day.fullDate} 有入库记录: ${inboundCount} 个包裹`);
        }
        return {
          ...day,
          hasInbound, // 有入库记录则显示指示器
        };
      });

      console.log(`共有 ${hasInboundDays} 天有入库记录`);
      this.setData({ calendarDays: updatedCalendarDays });
    },

    /**
     * 点击日期 - 支持跨月份选择的交互逻辑
     */
    onDateTap(e) {
      const { index } = e.currentTarget.dataset;
      const { calendarDays, startDate } = this.data;
      const clickedDay = calendarDays[index];

      // 允许选择所有日期，包括其他月份的日期
      // 如果点击的是其他月份的日期，先切换到对应月份
      if (!clickedDay.isCurrentMonth) {
        const clickedDate = new Date(clickedDay.fullDate);
        const clickedYear = clickedDate.getFullYear();
        const clickedMonth = clickedDate.getMonth() + 1;

        // 切换到对应月份
        this.setData({
          currentYear: clickedYear,
          currentMonth: clickedMonth
        });
        this.generateCalendar();
        this.loadDailyOutboundData();
        this.loadDailyInboundData();

        // 延迟执行日期选择逻辑，等待日历重新生成
        setTimeout(() => {
          this.selectDateByFullDate(clickedDay.fullDate);
        }, 100);
        return;
      }

      let newSelectedDates = [];
      let newStartDate = null;

      if (!startDate) {
        // 第一次点击：设为起始日期
        newStartDate = clickedDay.fullDate;
        newSelectedDates = [clickedDay.fullDate];
      } else {
        // 第二次点击
        if (clickedDay.fullDate === startDate) {
          // 点击相同日期：取消选择
          newStartDate = null;
          newSelectedDates = [];
        } else if (clickedDay.fullDate < startDate) {
          // 点击日期小于起始日期：将点击日期设为新的起始日期
          newStartDate = clickedDay.fullDate;
          newSelectedDates = [clickedDay.fullDate];
        } else {
          // 点击日期大于起始日期：完成区间选择
          newSelectedDates = this.getDateRange(startDate, clickedDay.fullDate);
          // 保持startDate不变，这样用户可以继续调整结束日期
          newStartDate = startDate;
        }
      }

      // 批量更新日历显示，减少重排
      const updatedCalendarDays = calendarDays.map((day) => {
        const isSelected = newSelectedDates.includes(day.fullDate);

        // 区间选择的特殊样式
        let isRangeStart = false;
        let isRangeEnd = false;
        let isInRange = false;

        if (newSelectedDates.length > 1) {
          isRangeStart = day.fullDate === newSelectedDates[0];
          isRangeEnd =
            day.fullDate === newSelectedDates[newSelectedDates.length - 1];
          isInRange = isSelected && !isRangeStart && !isRangeEnd;
        }

        return {
          ...day,
          isSelected,
          isRangeStart,
          isRangeEnd,
          isInRange,
        };
      });

      // 使用批量更新减少重排
      this.setData({
        selectedDates: newSelectedDates,
        startDate: newStartDate,
        calendarDays: updatedCalendarDays,
      });

      // 调试信息
      console.log("日期选择更新:", {
        selectedDates: newSelectedDates,
        startDate: newStartDate,
        clickedDate: clickedDay.fullDate,
        selectedCount: newSelectedDates.length,
      });

      // 触发选择状态更新事件
      this.triggerSelectionUpdate();
    },

    /**
     * 根据完整日期字符串选择日期
     */
    selectDateByFullDate(fullDate) {
      const { calendarDays, startDate } = this.data;

      // 找到对应的日期项
      const targetDay = calendarDays.find(day => day.fullDate === fullDate);
      if (!targetDay) {
        console.warn("未找到目标日期:", fullDate);
        return;
      }

      let newSelectedDates = [];
      let newStartDate = null;

      if (!startDate) {
        // 第一次点击：设为起始日期
        newStartDate = fullDate;
        newSelectedDates = [fullDate];
      } else {
        // 第二次点击
        if (fullDate === startDate) {
          // 点击相同日期：取消选择
          newStartDate = null;
          newSelectedDates = [];
        } else if (fullDate < startDate) {
          // 点击日期小于起始日期：将点击日期设为新的起始日期
          newStartDate = fullDate;
          newSelectedDates = [fullDate];
        } else {
          // 点击日期大于起始日期：完成区间选择
          newSelectedDates = this.getDateRange(startDate, fullDate);
          newStartDate = startDate;
        }
      }

      // 更新选择状态
      this.setData({
        selectedDates: newSelectedDates,
        startDate: newStartDate,
      });

      // 更新日历显示
      this.updateCalendarSelectionState();

      // 触发选择状态更新事件
      this.triggerSelectionUpdate();

      console.log("跨月份日期选择更新:", {
        selectedDates: newSelectedDates,
        startDate: newStartDate,
        clickedDate: fullDate,
      });
    },

    /**
     * 触发选择状态更新事件
     */
    triggerSelectionUpdate() {
      // 计算并更新总数显示
      const total = this.getSelectedDatesOutboundTotal();
      this.setData({ selectedDatesTotal: total });
      console.log("选择状态更新，总包裹数:", total);
    },

    /**
     * 获取日期区间
     */
    getDateRange(startDate, endDate) {
      const dates = [];
      const start = new Date(startDate);
      const end = new Date(endDate);

      for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        dates.push(
          this.formatDate(d.getFullYear(), d.getMonth() + 1, d.getDate())
        );
      }

      return dates;
    },

    /**
     * 上一月
     */
    onPrevMonth() {
      let { currentYear, currentMonth } = this.data;

      if (currentMonth === 1) {
        currentYear--;
        currentMonth = 12;
      } else {
        currentMonth--;
      }

      this.setData({ currentYear, currentMonth });
      this.generateCalendar();
      this.loadDailyOutboundData();
      this.loadDailyInboundData();
      // 不清除选择状态，支持跨月份选择
      this.updateCalendarSelectionState();
    },

    /**
     * 下一月
     */
    onNextMonth() {
      let { currentYear, currentMonth } = this.data;

      if (currentMonth === 12) {
        currentYear++;
        currentMonth = 1;
      } else {
        currentMonth++;
      }

      this.setData({ currentYear, currentMonth });
      this.generateCalendar();
      this.loadDailyOutboundData();
      this.loadDailyInboundData();
      // 不清除选择状态，支持跨月份选择
      this.updateCalendarSelectionState();
    },

    /**
     * 回到今天
     */
    onToday() {
      const today = new Date();
      const currentYear = today.getFullYear();
      const currentMonth = today.getMonth() + 1;

      this.setData({ currentYear, currentMonth });
      this.generateCalendar();
      this.loadDailyOutboundData();
      this.loadDailyInboundData();
      this.clearSelection();
    },

    /**
     * 显示年份选择器
     */
    onShowYearPicker() {
      this.setData({ showYearPicker: true });
    },

    /**
     * 关闭年份选择器
     */
    onCloseYearPicker() {
      this.setData({ showYearPicker: false });
    },

    /**
     * 年份选择变化
     */
    onYearChange(e) {
      // 暂存选择的年份，等确认时再应用
      const yearText = this.data.yearOptions[e.detail.index];
      this.tempSelectedYear = parseInt(yearText.replace('年', ''));
    },

    /**
     * 确认年份选择
     */
    onConfirmYear() {
      if (this.tempSelectedYear) {
        this.setData({
          currentYear: this.tempSelectedYear,
          showYearPicker: false,
        });
        this.generateCalendar();
        this.loadDailyOutboundData();
        this.loadDailyInboundData();
        this.clearSelection();
      } else {
        this.setData({ showYearPicker: false });
      }
    },

    /**
     * 显示月份选择器
     */
    onShowMonthPicker() {
      this.setData({ showMonthPicker: true });
    },

    /**
     * 关闭月份选择器
     */
    onCloseMonthPicker() {
      this.setData({ showMonthPicker: false });
    },

    /**
     * 月份选择变化
     */
    onMonthChange(e) {
      // 暂存选择的月份，等确认时再应用
      const monthText = this.data.monthOptions[e.detail.index];
      this.tempSelectedMonth = parseInt(monthText.replace('月', ''));
    },

    /**
     * 确认月份选择
     */
    onConfirmMonth() {
      if (this.tempSelectedMonth) {
        this.setData({
          currentMonth: this.tempSelectedMonth,
          showMonthPicker: false,
        });
        this.generateCalendar();
        this.loadDailyOutboundData();
        this.loadDailyInboundData();
        this.clearSelection();
      } else {
        this.setData({ showMonthPicker: false });
      }
    },

    /**
     * 更新日历选择状态（用于月份切换时保持选择状态）
     */
    updateCalendarSelectionState() {
      const { calendarDays, selectedDates } = this.data;

      if (selectedDates.length === 0) {
        return;
      }

      const updatedCalendarDays = calendarDays.map((day) => {
        const isSelected = selectedDates.includes(day.fullDate);

        // 区间选择的特殊样式
        let isRangeStart = false;
        let isRangeEnd = false;
        let isInRange = false;

        if (selectedDates.length > 1) {
          isRangeStart = day.fullDate === selectedDates[0];
          isRangeEnd = day.fullDate === selectedDates[selectedDates.length - 1];
          isInRange = isSelected && !isRangeStart && !isRangeEnd;
        }

        return {
          ...day,
          isSelected,
          isRangeStart,
          isRangeEnd,
          isInRange,
        };
      });

      this.setData({ calendarDays: updatedCalendarDays });
    },

    /**
     * 清除选择状态
     */
    clearSelection() {
      const { calendarDays } = this.data;
      const updatedCalendarDays = calendarDays.map((day) => ({
        ...day,
        isSelected: false,
        isRangeStart: false,
        isRangeEnd: false,
        isInRange: false,
      }));

      this.setData({
        selectedDates: [],
        selectionMode: "single",
        startDate: null,
        calendarDays: updatedCalendarDays,
        selectedDatesTotal: 0, // 清除总数
      });
    },

    /**
     * 触摸开始事件
     */
    onTouchStart(e) {
      this.setData({
        touchStartX: e.touches[0].clientX,
        touchStartY: e.touches[0].clientY,
      });
    },

    /**
     * 触摸移动事件
     */
    onTouchMove() {
      // 记录触摸移动，但不做处理
    },

    /**
     * 触摸结束事件
     */
    onTouchEnd(e) {
      const { touchStartX } = this.data;
      const touchEndX = e.changedTouches[0].clientX;
      const touchEndY = e.changedTouches[0].clientY;

      // 计算水平和垂直移动距离
      const deltaX = touchEndX - touchStartX;
      const deltaY = Math.abs(touchEndY - this.data.touchStartY);

      // 只有水平滑动距离大于50px且垂直滑动小于50px时才触发翻页
      if (Math.abs(deltaX) > 50 && deltaY < 50) {
        if (deltaX > 0) {
          // 右滑，显示上一月
          this.onPrevMonth();
        } else {
          // 左滑，显示下一月
          this.onNextMonth();
        }
      }
    },

    /**
     * 查询按钮点击事件
     */
    onQuery() {
      const { selectedDates, selectionMode, warehouseId, clothingId, oemClothingId } = this.data;

      if (selectedDates.length === 0) {
        wx.showToast({
          title: "请选择日期",
          icon: "none",
        });
        return;
      }

      // 构建查询参数
      let queryParams = {};

      if (selectionMode === "single" || selectedDates.length === 1) {
        // 单日查询
        queryParams = {
          start_date: selectedDates[0],
          end_date: selectedDates[0],
        };
      } else {
        // 日期区间查询
        queryParams = {
          start_date: selectedDates[0],
          end_date: selectedDates[selectedDates.length - 1],
        };
      }

      // 如果有仓库ID，添加到参数中
      if (warehouseId) {
        queryParams.warehouse_id = warehouseId;
      }

      // 如果有服装ID，添加到参数中
      if (clothingId) {
        queryParams.clothing_id = clothingId;
      }

      // 如果有OEM服装ID，添加到参数中
      if (oemClothingId) {
        queryParams.oem_clothing_id = oemClothingId;
      }

      console.log("日历查询参数:", queryParams);

      // 添加触感反馈
      try {
        wx.vibrateShort({
          type: "light",
        });
      } catch (error) {
        console.log("触感反馈不可用:", error);
      }

      console.log("触发查询事件，参数:", queryParams);

      // 触发查询事件，传递查询参数
      this.triggerEvent("query", queryParams);
    },

    /**
     * 快速选择今天
     */
    selectToday() {
      const today = new Date();
      const todayStr = this.formatDate(
        today.getFullYear(),
        today.getMonth() + 1,
        today.getDate()
      );

      // 如果不是当前月，先跳转到当前月
      const currentYear = today.getFullYear();
      const currentMonth = today.getMonth() + 1;

      if (
        this.data.currentYear !== currentYear ||
        this.data.currentMonth !== currentMonth
      ) {
        this.setData({ currentYear, currentMonth });
        this.generateCalendar();
        this.loadDailyOutboundData();
        this.loadDailyInboundData();
      }

      // 选中今天
      const { calendarDays } = this.data;
      const updatedCalendarDays = calendarDays.map((day) => ({
        ...day,
        isSelected: day.fullDate === todayStr,
        isRangeStart: false,
        isRangeEnd: false,
        isInRange: false,
      }));

      this.setData({
        selectedDates: [todayStr],
        selectionMode: "single",
        startDate: null,
        calendarDays: updatedCalendarDays,
      });

      // 更新总数显示
      this.triggerSelectionUpdate();

      // 添加触感反馈
      wx.vibrateShort({
        type: "light",
      });
    },

    /**
     * 获取选中日期的出库总数
     */
    getSelectedDatesOutboundTotal() {
      const { selectedDates, dailyOutboundData } = this.data;
      let total = 0;

      selectedDates.forEach((date) => {
        const count = dailyOutboundData[date] || 0;
        total += typeof count === 'number' ? count : parseFloat(count) || 0;
      });

      // 保留2位小数
      total = Math.round(total * 100) / 100;
      console.log("选中日期的出库总数:", total);
      return total;
    },

    /**
     * 清理指定仓库的缓存
     */
    clearCacheForWarehouse(warehouseId) {
      const { dataCache } = this.data;
      const newDataCache = {};

      // 保留其他仓库的缓存，清理指定仓库的缓存
      Object.keys(dataCache).forEach((key) => {
        const keyWarehouseId = key.split("-")[2];
        if (keyWarehouseId !== (warehouseId || "all")) {
          newDataCache[key] = dataCache[key];
        }
      });

      this.setData({ dataCache: newDataCache });
    },

    /**
     * 清理指定服装的缓存
     */
    clearCacheForClothing(clothingId) {
      const { dataCache } = this.data;
      const newDataCache = {};

      // 保留其他服装的缓存，清理指定服装的缓存
      Object.keys(dataCache).forEach((key) => {
        const keyClothingId = key.split("-")[3];
        if (keyClothingId !== (clothingId || "all")) {
          newDataCache[key] = dataCache[key];
        }
      });

      this.setData({ dataCache: newDataCache });
    },

    /**
     * 清理所有缓存
     */
    clearAllCache() {
      this.setData({ dataCache: {} });
    },

    /**
     * 刷新组件数据（供外部调用）
     */
    refreshData() {
      console.log("calendar-table组件刷新数据");
      // 清理缓存并重新加载数据
      this.clearAllCache();
      this.loadDailyOutboundData();
      this.loadDailyInboundData();
    },
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.init();
    },
  },

  /**
   * 监听属性变化
   */
  observers: {
    warehouseId: function (newVal, oldVal) {
      // 仓库ID变化时清理相关缓存并重新加载数据
      if (newVal !== oldVal) {
        this.clearCacheForWarehouse(oldVal);
        this.loadDailyOutboundData();
        this.loadDailyInboundData();
      }
    },
    clothingId: function (newVal, oldVal) {
      // 服装ID变化时清理相关缓存并重新加载数据
      if (newVal !== oldVal) {
        this.clearCacheForClothing(oldVal);
        this.loadDailyOutboundData();
        this.loadDailyInboundData();
      }
    },
    oemClothingId: function (newVal, oldVal) {
      // OEM服装ID变化时清理相关缓存并重新加载数据
      if (newVal !== oldVal) {
        this.clearCacheForClothing(oldVal);
        this.loadDailyOutboundData();
        this.loadDailyInboundData();
      }
    },
  },


});
