{"compilerOptions": {"target": "ES2020", "module": "node16", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node16", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}