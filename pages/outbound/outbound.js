// pages/outbound/outbound.js
import Api from "../../utils/api.js";
import TempStorage from "../../utils/storage.js";
import SearchCache from "../../utils/searchCache.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 库存列表（新交互流程：包含仓库信息的库存项）
    inventoryList: [],
    // 出库清单（新显示方式：每条记录独立显示）
    outboundList: [],
    // 购物车统计
    cartTotalPackages: 0,
    cartTotalPieces: 0,
    // 显示购物车浮窗
    showCart: false,
    // 显示服装信息弹窗
    showClothingInfo: false,
    // 当前选择的服装信息
    selectedClothingInfo: null,
    // 加载状态
    loading: false,
    // 搜索相关
    searchKeyword: "",
    isSearchMode: false,
    searchResults: [],
    searchLoading: false,
    originalWarehouseList: [], // 保存原始仓库列表（用于重置）
    // 智能下拉提示相关
    showSearchSuggestions: false,
    searchSuggestions: [],
    loadingSuggestions: false,
    searchInputFocused: false,
    // 搜索选项缓存相关
    allClothingNames: [], // 所有服装名称的缓存
    cacheLoading: false, // 缓存加载状态
  },

  // 定时器引用，用于清理
  _timers: [],

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // 确保 _timers 数组被正确初始化
    if (!this._timers) {
      this._timers = [];
    }
    this.loadWarehouses();

    // 检查并恢复临时数据
    this.checkAndRestoreTempData();

    // 初始化搜索选项缓存
    this.initializeSearchCache();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    console.log("=== 页面卸载开始 ===");

    // 检查是否是出库确认成功后的返回
    const app = getApp();
    const isAfterOutboundConfirm =
      app.globalData && app.globalData.shouldClearOutboundList;

    console.log("是否为出库确认后返回:", isAfterOutboundConfirm);
    console.log(
      "当前出库清单长度:",
      this.data.outboundList ? this.data.outboundList.length : 0
    );

    // 只有在非出库确认成功后的情况下才保存临时数据
    if (!isAfterOutboundConfirm) {
      console.log("正常页面卸载，保存临时数据");
      this.saveTempData();
    } else {
      console.log("出库确认成功后的页面卸载，跳过临时数据保存");
    }

    // 清理所有定时器
    if (this._timers && this._timers.length > 0) {
      this._timers.forEach((timer) => {
        if (timer) {
          clearTimeout(timer);
        }
      });
      this._timers = [];
    }

    // 清理搜索定时器
    if (this._searchTimer) {
      clearTimeout(this._searchTimer);
      this._searchTimer = null;
    }

    console.log("页面卸载完成，已清理定时器");
  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    console.log("=== 页面显示开始 ===");
    try {
      // 检查是否需要清空待出库清单（通过全局变量标记）
      const app = getApp();
      const shouldClear =
        app.globalData && app.globalData.shouldClearOutboundList;
      console.log("全局标记 shouldClearOutboundList:", shouldClear);
      console.log(
        "当前出库清单长度:",
        this.data.outboundList ? this.data.outboundList.length : 0
      );

      if (shouldClear) {
        console.log("出库成功后返回，清空待出库清单");
        this.clearOutboundListAfterConfirm();
        app.globalData.shouldClearOutboundList = false; // 重置标记
        console.log("已重置全局标记为 false");

        // 清空待出库清单后，重新加载仓库数据
        await this.loadWarehouses();
      } else {
        console.log("正常页面显示，刷新仓库列表");
        // 页面显示时刷新仓库列表，以防从仓库详情页面返回时有新增或修改
        await this.loadWarehouses();

        // 新交互流程：如果有待出库清单且处于搜索模式，需要重新计算库存显示
        if (this.data.outboundList && this.data.outboundList.length > 0 && this.data.isSearchMode) {
          console.log("从核对出货页面返回，重新计算搜索结果的库存显示");
          this.recalculateSearchInventoryDisplay();
        }
      }
    } catch (error) {
      console.error("页面显示时处理失败:", error);
      // 确保loading状态被重置
      this.setData({ loading: false });
    }
  },

  /**
   * 加载仓库列表 - 修复loading状态管理
   */
  async loadWarehouses() {
    try {
      this.setData({ loading: true });
      console.log("开始加载仓库列表");

      const response = await Api.getWarehouseList({ status: "active" });

      if (response.data.code === 200) {
        const warehouses = response.data.data.list || [];
        console.log(`加载到${warehouses.length}个仓库`);

        this.setData({
          originalWarehouseList: warehouses, // 保存原始列表（用于重置）
          inventoryList: [], // 初始状态不显示库存，等待用户搜索
          loading: false,
        });

        console.log(`仓库列表加载完成，共${warehouses.length}个仓库，等待用户搜索`);
        // 新交互流程：不自动加载仓库库存，等待用户搜索
      } else {
        console.error("加载仓库列表失败:", response.data.message);
        wx.showToast({
          title: response.data.message || "加载失败",
          icon: "none",
        });
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error("加载仓库列表异常:", error);
      wx.showToast({
        title: "加载失败",
        icon: "none",
      });
      this.setData({ loading: false });
    }
  },

  /* 已删除 loadWarehouseInventory 方法 - 新交互流程不再需要单独加载仓库库存 */

  /**
   * 处理新的classification_code库存数据结构 - 简化版本
   */
  processClassificationCodeInventory(inventoryData, warehouseId) {
    return inventoryData.map((item, index) => {
      // 生成唯一ID（基于classification_code）
      const unique_id = `${warehouseId}_${
        item.classification_code
      }_${Date.now()}_${index}`;

      return {
        ...item,
        warehouse_id: warehouseId,
        unique_id: unique_id,
        classification_code: item.classification_code,
        // 为每个contents项添加选择数量和可用数量
        contents: item.contents.map((content) => ({
          ...content,
          // selectedQuantity: 0,
          // available_quantity: content.current_quantity,
        })),
        selectedPackageQuantity: 0,
        availablePackageCount: item.package_count,
        availableTotalQuantity: item.total_quantity,
        // 如果是多货物包裹，初始化计算包数
        calculatedPackageQuantity:
          item.contents.length > 1 ? "0.00" : undefined,
      };
    });
  },

  /* 已删除 calculateInventoryStats 方法 - 新交互流程不再显示统计信息 */

  /**
   * 计算最终库存状态 - 根据package_type简化逻辑
   */
  calculateFinalInventoryStateNew(inventoryList, outboundList) {
    return inventoryList.map((inventoryItem) => {
      // 按classification_code匹配出库记录
      const relatedOutbounds = outboundList.filter((item) => {
        return (
          item.classification_code === inventoryItem.classification_code &&
          item.warehouse_id === inventoryItem.warehouse_id
        );
      });

      if (relatedOutbounds.length > 0) {
        // 根据package_type区分处理逻辑
        if (inventoryItem.package_type === "single") {
          // 单一包裹处理逻辑
          const totalOutboundPackages = relatedOutbounds.reduce(
            (sum, item) => sum + (parseFloat(item.package_count) || 0),
            0
          );

          // 计算动态包裹数
          const availablePackageCount = Math.max(
            0,
            inventoryItem.package_count - totalOutboundPackages
          );

          // 计算动态总件数
          const originalQuantityPerPackage =
            inventoryItem.contents[0].original_quantity;
          const availableTotalQuantity = Math.max(
            0,
            inventoryItem.total_quantity -
              originalQuantityPerPackage * totalOutboundPackages
          );

          // 更新contents的可用数量
          const updatedContents = inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: availableTotalQuantity,
            selectedQuantity: 0,
          }));

          return {
            ...inventoryItem,
            contents: updatedContents,
            availablePackageCount,
            availableTotalQuantity,
            selectedPackageQuantity: 0,
          };
        } else if (inventoryItem.package_type === "mixed") {
          // 混合包裹处理逻辑 - 不需要动态总件数、动态总包裹数
          const updatedContents = inventoryItem.contents.map((content) => {
            // 计算该content的总出库数量
            const totalOutboundForThisContent = relatedOutbounds.reduce(
              (sum, item) => {
                const matchingContent = item.contents.find(
                  (c) => c.sku === content.sku
                );
                return (
                  sum +
                  (matchingContent
                    ? matchingContent.outbound_quantity ||
                      matchingContent.selectedQuantity ||
                      0
                    : 0)
                );
              },
              0
            );

            // 动态可出库件数 = current_quantity - 出库件数
            return {
              ...content,
              available_quantity: Math.max(
                0,
                content.current_quantity - totalOutboundForThisContent
              ),
              selectedQuantity: 0,
            };
          });

          return {
            ...inventoryItem,
            contents: updatedContents,
            selectedPackageQuantity: 0,
            calculatedPackageQuantity: "0.00",
          };
        }
      } else {
        // 没有出库记录的库存项，恢复到原始状态
        if (inventoryItem.package_type === "single") {
          return {
            ...inventoryItem,
            availablePackageCount: inventoryItem.package_count,
            availableTotalQuantity: inventoryItem.total_quantity,
            selectedPackageQuantity: 0,
            contents: inventoryItem.contents.map((content) => ({
              ...content,
              available_quantity: content.current_quantity,
              selectedQuantity: 0,
            })),
          };
        } else if (inventoryItem.package_type === "mixed") {
          return {
            ...inventoryItem,
            selectedPackageQuantity: 0,
            calculatedPackageQuantity: "0.00",
            contents: inventoryItem.contents.map((content) => ({
              ...content,
              available_quantity: content.current_quantity,
              selectedQuantity: 0,
            })),
          };
        }
      }

      // 默认返回原始状态
      return inventoryItem;
    });
  },

  /**
   * 选择仓库 - 新交互流程中此方法已不再使用，保留以防兼容性问题
   */
  onSelectWarehouse(_e) {
    console.log("新交互流程中不再需要选择仓库，此方法已废弃");
    // 新交互流程中，用户直接通过搜索查看所有仓库的库存
    // 不再需要手动选择仓库
  },

  /**
   * 单货物包裹数量变化（步进器）- 步进器为0.5
   */
  onSingleItemQuantityChange(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const quantity = parseFloat(e.detail) || 0;

    const inventoryList = this.data.inventoryList;
    const updatedInventory = inventoryList.map((item, index) => {
      if (index === inventoryIndex) {
        return {
          ...item,
          selectedPackageQuantity: quantity,
        };
      }
      return item;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 多货物包裹中单个货物数量变化
   */
  onMultiItemQuantityChange(e) {
    const { inventoryIndex, contentIndex } = e.currentTarget.dataset;
    const quantity = Math.max(0, parseInt(e.detail) || 0);

    const inventoryList = this.data.inventoryList;
    const updatedInventory = inventoryList.map((item, index) => {
      if (index === inventoryIndex) {
        const updatedContents = item.contents.map((content, cIndex) => {
          if (cIndex === contentIndex) {
            // 限制输入数量不超过可用库存
            const availableQuantity =
              content.available_quantity !== undefined
                ? content.available_quantity
                : content.current_quantity;
            const limitedQuantity = Math.min(quantity, availableQuantity);
            return {
              ...content,
              selectedQuantity: limitedQuantity,
            };
          }
          return content;
        });

        // 计算出货包数
        const selectedTotalPieces = updatedContents.reduce(
          (sum, content) => sum + (content.selectedQuantity || 0),
          0
        );
        // 使用original_quantity而不是current_quantity，确保库存为0的货物也被计入包装规格
        const originalTotalPieces = updatedContents.reduce(
          (sum, content) => sum + content.original_quantity,
          0
        );
        const calculatedPackageQuantity =
          originalTotalPieces > 0
            ? Math.round((selectedTotalPieces / originalTotalPieces) * 100) /
              100
            : "0.00";

        return {
          ...item,
          contents: updatedContents,
          calculatedPackageQuantity,
        };
      }
      return item;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 单货物包裹出库按钮 - 新交互流程：直接使用库存项中的仓库信息
   */
  async onSingleItemOutbound(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const item = this.data.inventoryList[inventoryIndex];

    // 新交互流程：从库存项中获取仓库信息
    const warehouseInfo = item.warehouse_info || this.data.selectedWarehouse;

    if (!warehouseInfo) {
      wx.showToast({
        title: "仓库信息缺失",
        icon: "none",
      });
      return;
    }

    if (!item.selectedPackageQuantity || item.selectedPackageQuantity <= 0) {
      wx.showToast({
        title: "请选择出库数量",
        icon: "none",
      });
      return;
    }

    // 确保键盘收起，延迟执行出库操作
    wx.hideKeyboard();

    // 延迟100ms确保键盘收起和数据同步
    setTimeout(async () => {
      await this.addSingleItemToOutboundListNew(item, inventoryIndex, warehouseInfo);
      // 出库后只重置当前卡片的步进器为0
      this.resetSingleItemSelection(inventoryIndex);
    }, 100);
  },

  /**
   * 多货物包裹出库按钮 - 新交互流程：直接使用库存项中的仓库信息
   */
  async onMultiItemOutbound(e) {
    const { inventoryIndex } = e.currentTarget.dataset;
    const item = this.data.inventoryList[inventoryIndex];

    // 新交互流程：从库存项中获取仓库信息
    const warehouseInfo = item.warehouse_info || this.data.selectedWarehouse;

    if (!warehouseInfo) {
      wx.showToast({
        title: "仓库信息缺失",
        icon: "none",
      });
      return;
    }

    // 检查是否有选择的货物
    const selectedContents = item.contents.filter(
      (content) => content.selectedQuantity > 0
    );

    // 调试：检查选择的内容
    console.log("=== 多货物包裹出库调试 ===");
    console.log("原始item.contents:", item.contents);
    console.log("过滤后selectedContents:", selectedContents);
    console.log("selectedContents.length:", selectedContents.length);

    if (selectedContents.length === 0) {
      wx.showToast({
        title: "请选择要出库的货物",
        icon: "none",
      });
      return;
    }

    // 确保键盘收起，延迟执行出库操作
    wx.hideKeyboard();

    // 延迟100ms确保键盘收起和数据同步
    setTimeout(async () => {
      await this.addMultiItemToOutboundListNew(item, selectedContents, warehouseInfo);
      // 出库后只重置当前卡片的数字输入框为0
      this.resetMultiItemSelection(inventoryIndex);
    }, 100);
  },

  /**
   * 添加单货物包裹到出库清单 - 简化版本（保持向后兼容）
   */
  async addSingleItemToOutboundList(item) {
    const { selectedWarehouse } = this.data;
    if (!selectedWarehouse) {
      wx.showToast({
        title: "请先选择仓库",
        icon: "none",
      });
      return;
    }
    return this.addSingleItemToOutboundListNew(item, null, selectedWarehouse);
  },

  /**
   * 添加单货物包裹到出库清单 - 新交互流程版本
   */
  async addSingleItemToOutboundListNew(item, _inventoryIndex, warehouseInfo) {
    try {
      const outboundQuantity = item.selectedPackageQuantity;
      const content = item.contents[0];
      console.log("添加单货物包裹到出库清单:", item);

      if (outboundQuantity <= 0) {
        wx.showToast({
          title: "请选择出库数量",
          icon: "none",
        });
        return;
      }

      if (outboundQuantity > item.availablePackageCount) {
        wx.showToast({
          title: "出库数量超过可用库存",
          icon: "none",
        });
        return;
      }

      // 简化：直接计算总出库件数
      const totalOutboundPieces = Math.round(
        outboundQuantity * content.original_quantity
      );

      // 生成单货物包裹的显示名称
      const displayName = content.name || content.clothing_name || '未知服装';

      // 添加到出库清单 - 使用传入的仓库信息
      this.addToOutboundList({
        warehouse_id: warehouseInfo.warehouse_id,
        warehouse_name: warehouseInfo.name,
        classification_code: item.classification_code,
        package_type: item.package_type,
        package_count: outboundQuantity,
        total_pieces: totalOutboundPieces,
        // 保留用于显示的基本信息，确保有outbound_quantity
        contents: [
          {
            ...content,
            outbound_quantity: totalOutboundPieces,
          },
        ],
        // 单货物包裹的显示名称
        display_name: displayName,
        outbound_clothing_count: 1,
      });
    } catch (error) {
      console.error("添加单货物包裹到出库清单失败:", error);
      wx.showToast({
        title: "添加到出库清单失败",
        icon: "none",
      });
    }
  },

  /**
   * 添加多货物包裹到出库清单 - 简化版本（保持向后兼容）
   */
  async addMultiItemToOutboundList(item, selectedContents) {
    const { selectedWarehouse } = this.data;
    if (!selectedWarehouse) {
      wx.showToast({
        title: "请先选择仓库",
        icon: "none",
      });
      return;
    }
    return this.addMultiItemToOutboundListNew(item, selectedContents, selectedWarehouse);
  },

  /**
   * 添加多货物包裹到出库清单 - 新交互流程版本
   */
  async addMultiItemToOutboundListNew(item, selectedContents, warehouseInfo) {
    try {
      console.log("多货物包裹出库参数:", { item, selectedContents });

      // 验证 selectedContents 是数组
      if (!Array.isArray(selectedContents)) {
        console.error("selectedContents 不是数组:", selectedContents);
        wx.showToast({
          title: "数据格式错误",
          icon: "none",
        });
        return;
      }

      // 计算出货总件数（四舍五入避免半件衣服）
      const outboundTotalPieces = Math.round(
        selectedContents.reduce(
          (sum, content) => sum + content.selectedQuantity,
          0
        )
      );

      // 计算正确的包数：当前混合包裹各个货物总的出库数除以混合包裹内各个货物original_quantity之和，保留2位小数
      const totalOriginalQuantity = item.contents.reduce(
        (sum, content) =>
          sum + (content.original_quantity || content.current_quantity),
        0
      );
      const calculatedPackageQuantity =
        totalOriginalQuantity > 0
          ? Math.round((outboundTotalPieces / totalOriginalQuantity) * 100) /
            100
          : 0;

      // 添加到出库清单 - 使用传入的仓库信息
      // 修复：对于混合包裹，保留所有contents，但只有选中的才有outbound_quantity
      const allContentsWithOutbound = item.contents.map((content) => {
        const selectedContent = selectedContents.find(sc => sc.sku === content.sku);
        return {
          ...content,
          outbound_quantity: selectedContent ? selectedContent.selectedQuantity : 0,
        };
      });

      // 生成出库服装名称字符串
      const outboundContents = allContentsWithOutbound.filter(c => c.outbound_quantity > 0);
      const clothingNames = outboundContents.map(c => c.name || c.clothing_name || '未知服装');
      const displayName = clothingNames.join(' + ');

      const outboundItem = {
        warehouse_id: warehouseInfo.warehouse_id,
        warehouse_name: warehouseInfo.name,
        classification_code: item.classification_code,
        package_type: item.package_type,
        package_count: calculatedPackageQuantity,
        total_pieces: outboundTotalPieces,
        // 保留完整的contents数组
        contents: allContentsWithOutbound,
        // 新增：连接后的服装名称字符串
        display_name: displayName,
        outbound_clothing_count: outboundContents.length,
      };

      // 调试：检查混合包裹数据结构
      if (item.package_type === "mixed") {
        console.log("混合包裹出库数据:", {
          package_type: outboundItem.package_type,
          display_name: outboundItem.display_name,
          outbound_clothing_count: outboundItem.outbound_clothing_count
        });
      }

      this.addToOutboundList(outboundItem);
    } catch (error) {
      console.error("添加多货物包裹到出库清单失败:", error);
      wx.showToast({
        title: "添加到出库清单失败",
        icon: "none",
      });
    }
  },

  /**
   * 添加到出库清单 - 每次出库都是独立记录
   */
  addToOutboundList(outboundItem) {
    const { outboundList = [] } = this.data;

    // 生成唯一ID（使用时间戳和随机数确保唯一性）
    const itemId = `${outboundItem.warehouse_id}_${
      outboundItem.classification_code
    }_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    const newItem = {
      ...outboundItem,
      id: itemId,
      created_at: new Date().toISOString(),
    };

    const updatedList = [...outboundList, newItem];

    // 简化的库存显示更新
    this.updateInventoryAfterOutboundSimple(outboundItem);

    // 更新购物车统计
    this.updateCartStatistics(updatedList);

    this.setData({
      outboundList: updatedList,
    });

    // 调试：检查出库清单显示名称
    const mixedItems = updatedList.filter(listItem => listItem.package_type === 'mixed');
    if (mixedItems.length > 0) {
      console.log("=== 混合包裹显示名称调试 ===");
      mixedItems.forEach((mixedItem, index) => {
        console.log(`混合包裹${index + 1}:`, {
          display_name: mixedItem.display_name,
          outbound_count: mixedItem.outbound_clothing_count
        });
      });
    }

    // 自动保存临时数据
    this.saveTempData();
  },

  /**
   * 简化的库存显示更新 - 根据package_type区分处理逻辑（支持新交互流程）
   */
  updateInventoryAfterOutboundSimple(outboundItem) {
    const updatedInventory = this.data.inventoryList.map((inventoryItem) => {
      // 按classification_code匹配，同时考虑仓库信息
      const itemWarehouseId = inventoryItem.warehouse_info ?
        inventoryItem.warehouse_info.warehouse_id :
        inventoryItem.warehouse_id;

      if (
        inventoryItem.classification_code === outboundItem.classification_code &&
        itemWarehouseId === outboundItem.warehouse_id
      ) {
        if (inventoryItem.package_type === "single") {
          // 单一包裹处理逻辑
          const outboundPackageCount = outboundItem.package_count;
          const originalQuantityPerPackage =
            inventoryItem.contents[0].original_quantity;

          // 更新动态包裹数
          const newAvailablePackageCount = Math.max(
            0,
            inventoryItem.availablePackageCount - outboundPackageCount
          );

          // 更新动态总件数
          const newAvailableTotalQuantity = Math.max(
            0,
            inventoryItem.availableTotalQuantity -
              originalQuantityPerPackage * outboundPackageCount
          );

          // 更新contents的可用数量（单一包裹中所有content的可用数量都等于总件数）
          const updatedContents = inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: newAvailableTotalQuantity,
            selectedQuantity: 0,
          }));

          return {
            ...inventoryItem,
            contents: updatedContents,
            availablePackageCount: newAvailablePackageCount,
            availableTotalQuantity: newAvailableTotalQuantity,
            selectedPackageQuantity: 0,
          };
        } else if (inventoryItem.package_type === "mixed") {
          // 混合包裹处理逻辑 - 每种服装的可出库数量独立计算
          const updatedContents = inventoryItem.contents.map((content) => {
            // 查找出库清单中对应的content
            const outboundContent = outboundItem.contents.find(
              (outContent) => outContent.sku === content.sku
            );

            if (outboundContent) {
              // 动态可出库件数 = current_quantity - 出库件数
              const newAvailableQuantity = Math.max(
                0,
                (content.available_quantity || content.current_quantity) -
                  (outboundContent.outbound_quantity ||
                    outboundContent.selectedQuantity ||
                    0)
              );

              return {
                ...content,
                available_quantity: newAvailableQuantity,
                selectedQuantity: 0,
              };
            } else {
              // 没有出库的content，只重置选择状态
              return {
                ...content,
                selectedQuantity: 0,
              };
            }
          });

          return {
            ...inventoryItem,
            contents: updatedContents,
            selectedPackageQuantity: 0,
            calculatedPackageQuantity: "0.00",
          };
        }
      }
      return inventoryItem;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /* 已完全删除废弃的 updateOutboundListDisplaySimple 方法 */

  /**
   * 点击服装名称
   */
  async onClothingNameTap(e) {
    try {
      const { item, content } = e.currentTarget.dataset;

      // 添加调试信息
      console.log("点击服装名称，完整事件数据:", e);
      console.log("点击服装名称，提取的数据:", { item, content });
      console.log("点击服装名称，dataset:", e.currentTarget.dataset);

      // 检查数据是否存在
      if (!item && !content) {
        console.error("点击事件数据缺失，完整dataset:", e.currentTarget.dataset);
        wx.showToast({
          title: "数据错误",
          icon: "none",
        });
        return;
      }

      // 如果有content数据（库存搜索结果中的调用方式）
      if (content) {
        console.log("使用content数据显示服装信息:", content);
        this.showClothingInfo(content);
        return;
      }

      // 如果有item数据（出库清单中的调用方式）
      if (item) {
        console.log("使用item数据，item类型:", typeof item, "item内容:", item);

        // 检查item是否有必要的属性
        if (!item.package_type) {
          console.error("item缺少package_type属性:", item);
          wx.showToast({
            title: "数据格式错误",
            icon: "none",
          });
          return;
        }

        // 对于单货物包裹，直接显示该服装信息
        if (item.package_type === 'single' && item.contents && item.contents.length > 0) {
          const contentData = item.contents[0];
          console.log("单货物包裹，显示服装信息:", contentData);
          this.showClothingInfo(contentData);
          return;
        }

        // 对于混合包裹，显示第一个出库服装的信息
        if (item.package_type === 'mixed' && item.contents) {
          const outboundContents = item.contents.filter(c => c.outbound_quantity > 0);
          if (outboundContents.length > 0) {
            // 显示第一个服装的信息
            const contentData = outboundContents[0];
            console.log("混合包裹，显示第一个服装信息:", contentData);
            this.showClothingInfo(contentData);
            return;
          } else {
            console.warn("混合包裹中没有找到出库服装");
          }
        }
      }

      // 如果都没有找到合适的数据
      console.error("无法处理点击事件，数据:", { item, content });
      wx.showToast({
        title: "无法获取服装信息",
        icon: "none",
      });
    } catch (error) {
      console.error("点击服装名称处理出错:", error);
      wx.showToast({
        title: "处理出错",
        icon: "none",
      });
    }
  },

  /**
   * 点击出库清单中的服装名称
   */
  async onOutboundItemNameTap(e) {
    try {
      const { index } = e.currentTarget.dataset;
      const outboundList = this.data.outboundList || [];

      console.log("点击出库清单项目，索引:", index);
      console.log("出库清单长度:", outboundList.length);

      if (index === undefined || index < 0 || index >= outboundList.length) {
        console.error("无效的出库清单索引:", index);
        wx.showToast({
          title: "数据错误",
          icon: "none",
        });
        return;
      }

      const item = outboundList[index];
      console.log("出库清单项目数据:", item);

      if (!item || !item.contents || item.contents.length === 0) {
        console.error("出库清单项目数据无效:", item);
        wx.showToast({
          title: "数据无效",
          icon: "none",
        });
        return;
      }

      // 对于单货物包裹，直接显示该服装信息
      if (item.package_type === 'single') {
        const contentData = item.contents[0];
        console.log("单货物包裹，显示服装信息:", contentData);
        this.showClothingInfo(contentData);
        return;
      }

      // 对于混合包裹，显示第一个出库服装的信息
      if (item.package_type === 'mixed') {
        const outboundContents = item.contents.filter(c => c.outbound_quantity > 0);
        if (outboundContents.length > 0) {
          const contentData = outboundContents[0];
          console.log("混合包裹，显示第一个服装信息:", contentData);
          this.showClothingInfo(contentData);
          return;
        } else {
          console.warn("混合包裹中没有找到出库服装");
          wx.showToast({
            title: "没有出库服装",
            icon: "none",
          });
          return;
        }
      }

      console.error("未知的包裹类型:", item.package_type);
      wx.showToast({
        title: "未知包裹类型",
        icon: "none",
      });

    } catch (error) {
      console.error("点击出库清单项目处理出错:", error);
      wx.showToast({
        title: "处理出错",
        icon: "none",
      });
    }
  },

  /**
   * 显示服装信息
   */
  async showClothingInfo(content) {
    // 显示加载状态
    wx.showLoading({
      title: "加载中...",
      mask: true,
    });

    try {
      let clothingInfo;

      if (content.is_oem) {
        // 获取OEM服装详细信息
        console.log(
          "获取OEM服装信息，ID:",
          content.oem_clothing_id || content.clothing_id
        );
        const response = await Api.getOemClothingInfo({
          oem_clothing_id: content.oem_clothing_id || content.clothing_id,
        });

        console.log("OEM服装API响应:", response);

        // 检查多种可能的响应格式
        let oemData = null;
        if (response.data) {
          if (response.data.code === 200) {
            // 标准格式：{ code: 200, data: {...}, message: "..." }
            oemData = response.data.data;
          } else if (response.data.oem_clothing_id) {
            // 直接返回数据格式
            oemData = response.data;
          }
        } else if (response.oem_clothing_id) {
          // 最简单的格式，直接是数据
          oemData = response;
        }

        if (oemData && oemData.oem_clothing_id) {
          clothingInfo = {
            oem_clothing_id: oemData.oem_clothing_id,
            oem_clothing_name: oemData.oem_clothing_name,
            oem_supplier: oemData.oem_supplier || "",
            classification: oemData.classification || "",
            price: oemData.price || 0,
            img: oemData.img || [],
            is_oem: true,
            size: oemData.size || "",
            style: oemData.style || "",
            order_quantity: oemData.order_quantity || 0,
            shipments: oemData.shipments || 0,
            in_pcs: oemData.in_pcs || 0,
          };
        } else {
          console.error("OEM服装API响应格式不正确:", response);
          throw new Error("获取OEM服装信息失败");
        }
      } else {
        // 获取普通服装详细信息
        console.log("获取普通服装信息，ID:", content.clothing_id);
        const response = await Api.getClothingInfo({
          clothing_id: content.clothing_id,
        });

        console.log("普通服装API响应:", response);

        // 检查多种可能的响应格式
        let clothingData = null;
        if (response.data) {
          if (response.data.code === 200) {
            // 标准格式：{ code: 200, data: {...}, message: "..." }
            clothingData = response.data.data;
          } else if (response.data.clothing_id) {
            // 直接返回数据格式
            clothingData = response.data;
          }
        } else if (response.clothing_id) {
          // 最简单的格式，直接是数据
          clothingData = response;
        }

        if (clothingData && clothingData.clothing_id) {
          clothingInfo = {
            clothing_id: clothingData.clothing_id,
            clothing_name: clothingData.clothing_name,
            sku: clothingData.sku || "",
            supplier: clothingData.supplier || "",
            price: clothingData.price || 0,
            img: clothingData.img || [],
            is_oem: false,
            group_classification: clothingData.group_classification || [],
            size: clothingData.size || "",
            style: clothingData.style || "",
            long_or_short_sleeve: clothingData.long_or_short_sleeve || "",
            pocket_type: clothingData.pocket_type || "",
            order_quantity: clothingData.order_quantity || 0,
            clipping_pcs: clothingData.clipping_pcs || 0,
            shipments: clothingData.shipments || 0,
          };
        } else {
          console.error("普通服装API响应格式不正确:", response);
          throw new Error("获取服装信息失败");
        }
      }

      console.log("构造的服装信息:", clothingInfo);

      this.setData({
        selectedClothingInfo: clothingInfo,
        showClothingInfo: true,
      });
    } catch (error) {
      console.error("获取服装详细信息失败:", error);
      wx.showToast({
        title: "获取服装信息失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 关闭服装信息弹窗
   */
  closeClothingInfo() {
    this.setData({
      showClothingInfo: false,
      selectedClothingInfo: null,
    });
  },

  /**
   * 切换购物车显示
   */
  toggleCart() {
    this.setData({
      showCart: !this.data.showCart,
    });
  },

  /**
   * 重置选择数量 - 出库后将步进器归零
   */
  resetSelectionQuantities() {
    const updatedInventory = this.data.inventoryList.map((inventoryItem) => ({
      ...inventoryItem,
      selectedPackageQuantity: 0, // 单货物包裹步进器归零
      calculatedPackageQuantity:
        inventoryItem.contents.length > 1 ? "0.00" : undefined, // 多货物包裹计算包数归零
      contents: inventoryItem.contents.map((content) => ({
        ...content,
        selectedQuantity: 0, // 多货物包裹中每个货物的选择数量归零
      })),
    }));

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 重置单货物包裹的选择数量 - 只重置指定卡片的步进器
   */
  resetSingleItemSelection(inventoryIndex) {
    const inventoryList = this.data.inventoryList;
    const updatedInventory = inventoryList.map((item, index) => {
      if (index === inventoryIndex) {
        return {
          ...item,
          selectedPackageQuantity: 0, // 只重置当前卡片的步进器为0
        };
      }
      return item;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 重置多货物包裹的选择数量 - 只重置指定卡片的数字输入框
   */
  resetMultiItemSelection(inventoryIndex) {
    const inventoryList = this.data.inventoryList;
    const updatedInventory = inventoryList.map((item, index) => {
      if (index === inventoryIndex) {
        return {
          ...item,
          calculatedPackageQuantity: "0.00", // 重置计算包数为0
          contents: item.contents.map((content) => ({
            ...content,
            selectedQuantity: 0, // 重置所有货物的选择数量为0
          })),
        };
      }
      return item;
    });

    this.setData({
      inventoryList: updatedInventory,
    });
  },

  /**
   * 出库确认后清空待出库清单并关闭弹窗
   */
  clearOutboundListAfterConfirm() {
    // 恢复所有库存显示 - 根据package_type区分处理
    const updatedInventory = this.data.inventoryList.map((inventoryItem) => {
      if (inventoryItem.package_type === "single") {
        return {
          ...inventoryItem,
          availablePackageCount: inventoryItem.package_count,
          availableTotalQuantity: inventoryItem.total_quantity,
          selectedPackageQuantity: 0, // 步进器归零
          contents: inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: content.current_quantity,
            selectedQuantity: 0, // 选择数量归零
          })),
        };
      } else if (inventoryItem.package_type === "mixed") {
        return {
          ...inventoryItem,
          selectedPackageQuantity: 0, // 步进器归零
          calculatedPackageQuantity: "0.00",
          contents: inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: content.current_quantity,
            selectedQuantity: 0, // 选择数量归零
          })),
        };
      } else {
        // 默认处理（兼容旧数据）
        return {
          ...inventoryItem,
          availablePackageCount: inventoryItem.package_count,
          availableTotalQuantity: inventoryItem.total_quantity,
          selectedPackageQuantity: 0,
          calculatedPackageQuantity:
            inventoryItem.contents.length > 1 ? "0.00" : undefined,
          contents: inventoryItem.contents.map((content) => ({
            ...content,
            available_quantity: content.current_quantity,
            selectedQuantity: 0,
          })),
        };
      }
    });

    this.setData({
      outboundList: [],
      cartTotalPackages: 0,
      cartTotalPieces: 0,
      showCart: false, // 关闭待出库清单弹窗
      inventoryList: updatedInventory,
    });

    // 清除临时数据
    this.clearTempData();

    console.log("待出库清单已清空，弹窗已关闭，临时数据已清除");
  },

  /* 已删除 recalculateInventoryDisplayOnReturn 方法 - 新交互流程使用 recalculateSearchInventoryDisplay */

  /**
   * 从出库清单中移除 - 旧版本（保留兼容性）
   */
  removeFromCart(e) {
    const { cartKey } = e.currentTarget.dataset;

    // 找到要删除的显示项
    const displayItem = this.data.groupedCart
      .flatMap((group) => group.items)
      .find((item) => item.display_id === cartKey);

    if (!displayItem) {
      wx.showToast({
        title: "项目不存在",
        icon: "none",
      });
      return;
    }

    console.log("删除出库清单项:", displayItem);

    // 从出库清单中删除所有相同classification_code的项目
    const updatedOutboundList = this.data.outboundList.filter(
      (item) => item.classification_code !== displayItem.classification_code
    );

    console.log("删除前出库清单长度:", this.data.outboundList.length);
    console.log("删除后出库清单长度:", updatedOutboundList.length);

    // 修复库存恢复逻辑：重新计算所有库存项的状态
    const restoredInventoryList = this.restoreInventoryAfterRemoval(
      this.data.inventoryList,
      displayItem,
      updatedOutboundList
    );

    this.setData({
      inventoryList: restoredInventoryList,
      outboundList: updatedOutboundList,
    });

    // 更新购物车统计
    this.updateCartStatistics(updatedOutboundList);

    // 自动保存临时数据
    this.saveTempData();

    wx.showToast({
      title: "已移除",
      icon: "success",
      duration: 500,
    });
  },

  /**
   * 从出库清单中移除 - 新版本（按ID删除单条记录）
   */
  removeFromCartNew(e) {
    const { outboundId } = e.currentTarget.dataset;

    // 找到要删除的出库记录
    const itemToRemove = this.data.outboundList.find(item => item.id === outboundId);

    if (!itemToRemove) {
      wx.showToast({
        title: "项目不存在",
        icon: "none",
      });
      return;
    }

    console.log("删除出库清单项:", itemToRemove);

    // 从出库清单中删除指定的单条记录
    const updatedOutboundList = this.data.outboundList.filter(
      (item) => item.id !== outboundId
    );

    console.log("删除前出库清单长度:", this.data.outboundList.length);
    console.log("删除后出库清单长度:", updatedOutboundList.length);

    // 恢复库存状态
    const restoredInventoryList = this.restoreInventoryAfterRemovalNew(
      this.data.inventoryList,
      itemToRemove,
      updatedOutboundList
    );

    // 修复：确保删除后的数据包含所有必要的字段
    const fixedUpdatedList = updatedOutboundList.map(item => {
      if (!item.display_name && item.contents) {
        // 重新生成显示名称
        const outboundContents = item.contents.filter(c => c.outbound_quantity > 0);
        const clothingNames = outboundContents.map(c => c.name || c.clothing_name || '未知服装');
        item.display_name = clothingNames.join(' + ');
        item.outbound_clothing_count = outboundContents.length;
      }
      return item;
    });

    this.setData({
      inventoryList: restoredInventoryList,
      outboundList: fixedUpdatedList,
    });

    // 更新购物车统计
    this.updateCartStatistics(fixedUpdatedList);

    // 自动保存临时数据
    this.saveTempData();

    wx.showToast({
      title: "已移除",
      icon: "success",
      duration: 500,
    });
  },

  /**
   * 恢复库存状态（修复混合包裹库存恢复问题）- 旧版本
   */
  restoreInventoryAfterRemoval(inventoryList, removedItem, remainingOutboundList) {
    return inventoryList.map((inventoryItem) => {
      // 检查是否是被删除项目对应的库存项
      const itemWarehouseId = inventoryItem.warehouse_info ?
        inventoryItem.warehouse_info.warehouse_id :
        inventoryItem.warehouse_id;

      if (inventoryItem.classification_code === removedItem.classification_code) {
        // 找到对应仓库的剩余出库清单
        const warehouseOutbounds = remainingOutboundList.filter(
          (item) => item.warehouse_id === itemWarehouseId &&
                    item.classification_code === inventoryItem.classification_code
        );

        if (warehouseOutbounds.length === 0) {
          // 没有剩余出库记录，完全恢复到原始状态
          if (inventoryItem.package_type === "mixed") {
            return {
              ...inventoryItem,
              selectedPackageQuantity: 0,
              calculatedPackageQuantity: "0.00",
              contents: inventoryItem.contents.map((content) => ({
                ...content,
                available_quantity: content.current_quantity,
                selectedQuantity: 0,
              })),
            };
          } else if (inventoryItem.package_type === "single") {
            return {
              ...inventoryItem,
              availablePackageCount: inventoryItem.package_count,
              availableTotalQuantity: inventoryItem.total_quantity,
              selectedPackageQuantity: 0,
              contents: inventoryItem.contents.map((content) => ({
                ...content,
                available_quantity: content.current_quantity,
                selectedQuantity: 0,
              })),
            };
          }
        } else {
          // 有剩余出库记录，重新计算状态
          const finalState = this.calculateFinalInventoryStateNew([inventoryItem], warehouseOutbounds);
          return finalState[0] || inventoryItem;
        }
      }

      return inventoryItem;
    });
  },

  /**
   * 恢复库存状态 - 新版本（按单条记录恢复）
   */
  restoreInventoryAfterRemovalNew(inventoryList, removedItem, remainingOutboundList) {
    return inventoryList.map((inventoryItem) => {
      // 检查是否是被删除项目对应的库存项
      const itemWarehouseId = inventoryItem.warehouse_info ?
        inventoryItem.warehouse_info.warehouse_id :
        inventoryItem.warehouse_id;

      if (inventoryItem.classification_code === removedItem.classification_code &&
          itemWarehouseId === removedItem.warehouse_id) {

        // 找到对应仓库和classification_code的剩余出库清单
        const warehouseOutbounds = remainingOutboundList.filter(
          (item) => item.warehouse_id === itemWarehouseId &&
                    item.classification_code === inventoryItem.classification_code
        );

        if (warehouseOutbounds.length === 0) {
          // 没有剩余出库记录，完全恢复到原始状态
          if (inventoryItem.package_type === "mixed") {
            return {
              ...inventoryItem,
              selectedPackageQuantity: 0,
              calculatedPackageQuantity: "0.00",
              contents: inventoryItem.contents.map((content) => ({
                ...content,
                available_quantity: content.current_quantity,
                selectedQuantity: 0,
              })),
            };
          } else if (inventoryItem.package_type === "single") {
            return {
              ...inventoryItem,
              availablePackageCount: inventoryItem.package_count,
              availableTotalQuantity: inventoryItem.total_quantity,
              selectedPackageQuantity: 0,
              contents: inventoryItem.contents.map((content) => ({
                ...content,
                available_quantity: content.current_quantity,
                selectedQuantity: 0,
              })),
            };
          }
        } else {
          // 有剩余出库记录，重新计算状态
          const finalState = this.calculateFinalInventoryStateNew([inventoryItem], warehouseOutbounds);
          return finalState[0] || inventoryItem;
        }
      }

      return inventoryItem;
    });
  },

  /**
   * 更新购物车统计信息
   */
  updateCartStatistics(outboundList) {
    const cartTotalPackages = outboundList.reduce(
      (sum, item) => sum + (parseFloat(item.package_count) || 0),
      0
    );
    const cartTotalPieces = outboundList.reduce(
      (sum, item) => sum + (item.total_pieces || 0),
      0
    );

    this.setData({
      cartTotalPackages: Math.round(cartTotalPackages * 100) / 100,
      cartTotalPieces,
    });
  },
  /**
   * 生成完整的出库数据结构 - 按仓库分组，包含完整仓库信息
   */
  generateCompleteOutboundData(outboundList) {
    const warehouseGroups = new Map();

    outboundList.forEach((item) => {
      // 获取仓库信息
      if (!warehouseGroups.has(item.warehouse_id)) {
        warehouseGroups.set(item.warehouse_id, {
          warehouse_id: item.warehouse_id,
          warehouse_name: item.warehouse_name,
          warehouse_address: item.warehouse_address || "",
          items: [],
          total_packages: 0,
          total_pieces: 0,
        });
      }

      const warehouseGroup = warehouseGroups.get(item.warehouse_id);

      // 查找是否已有相同的classification_code
      const existingItem = warehouseGroup.items.find(
        (i) => i.classification_code === item.classification_code
      );

      if (existingItem) {
        // 累加包裹数和件数
        existingItem.package_count += item.package_count;
        existingItem.total_pieces += item.total_pieces;

        // 合并contents的出库数量
        item.contents.forEach((newContent) => {
          const existingContent = existingItem.contents.find(
            (content) => content.sku === newContent.sku
          );
          if (existingContent) {
            existingContent.outbound_quantity =
              (existingContent.outbound_quantity || 0) +
              (newContent.outbound_quantity ||
                newContent.selectedQuantity ||
                0);
          } else {
            existingItem.contents.push({
              ...newContent,
              outbound_quantity:
                newContent.outbound_quantity ||
                newContent.selectedQuantity ||
                0,
            });
          }
        });
      } else {
        // 新增项目
        warehouseGroup.items.push({
          classification_code: item.classification_code,
          package_count: item.package_count,
          total_pieces: item.total_pieces,
          package_type: item.package_type || "single",
          clothing_name: this.getClothingNameFromItem(item),
          contents: item.contents.map((content) => ({
            ...content,
            outbound_quantity:
              content.outbound_quantity || content.selectedQuantity || 0,
          })),
        });
      }

      // 更新统计数据
      warehouseGroup.total_packages += item.package_count;
      warehouseGroup.total_pieces += item.total_pieces;
    });

    // 转换Map为数组格式
    const result = Array.from(warehouseGroups.values());
    return result;
  },

  /**
   * 从出库项获取服装名称
   */
  getClothingNameFromItem(item) {
    if (item.contents && item.contents.length > 0) {
      if (item.contents.length === 1) {
        return item.contents[0].name || "未知商品";
      } else {
        return item.contents
          .map((content) => content.name || "未知商品")
          .join(" + ");
      }
    }
    return this.getClothingNameFromClassificationCode(item.classification_code);
  },

  /**
   * 从classification_code获取服装名称
   */
  getClothingNameFromClassificationCode(classificationCode) {
    if (!classificationCode) return "未知商品";
    const parts = classificationCode.split("_");
    if (parts.length >= 2) {
      const clothingId = parts[0] + "_" + parts[1];
      return `服装 ${clothingId}`;
    }
    return "未知商品";
  },

  /**
   * 确认出库 - 跳转到核对出货页面
   */
  async confirmOutbound() {
    const { outboundList } = this.data;

    if (outboundList.length === 0) {
      wx.showToast({
        title: "请先添加出库商品",
        icon: "none",
      });
      return;
    }

    // 计算总计数据
    const totalPackages =
      Math.round(
        outboundList.reduce(
          (sum, item) => sum + (parseFloat(item.package_count) || 0),
          0
        ) * 100
      ) / 100;
    const totalPieces = outboundList.reduce(
      (sum, item) => sum + (item.total_pieces || 0),
      0
    );

    // 生成完整的出库数据结构（包含仓库信息和按package_type分组）
    const completeOutboundData =
      this.generateCompleteOutboundData(outboundList);

    // 跳转到核对出货页面，传递完整的数据结构
    const orderData = {
      warehouses: completeOutboundData, // 完整的仓库分组数据
      total_packages: totalPackages,
      total_pieces: totalPieces,
    };

    console.log("传递给确认页面的完整数据:", orderData);

    // 注意：不在这里清除临时数据，只有在出库成功后才清除
    // 这样如果用户取消或出库失败，数据仍然保留

    wx.navigateTo({
      url: `/pages/outbound-confirm/outbound-confirm?orderData=${encodeURIComponent(
        JSON.stringify(orderData)
      )}`,
    });
  },

  /**
   * 搜索输入变化 - 适配原生input，添加智能下拉提示
   */
  onSearchInput(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword,
    });

    // 实时搜索建议
    if (keyword.trim().length > 0) {
      // 防抖处理，避免频繁请求
      if (this._searchTimer) {
        clearTimeout(this._searchTimer);
      }
      this._searchTimer = setTimeout(() => {
        this.loadSearchSuggestions(keyword.trim());
      }, 300);
    } else {
      this.setData({
        showSearchSuggestions: false,
        searchSuggestions: [],
      });
    }
  },

  /**
   * 搜索框获得焦点
   */
  onSearchFocus() {
    console.log("搜索框获得焦点事件触发");
    this.setData({
      searchInputFocused: true,
    });

    // 如果有搜索关键词，显示建议
    if (this.data.searchKeyword.trim().length > 0) {
      this.loadSearchSuggestions(this.data.searchKeyword.trim());
    }
  },

  /**
   * 搜索框失去焦点
   */
  onSearchBlur() {
    console.log("搜索框失去焦点事件触发");
    // 延迟隐藏建议，给点击建议项留出时间
    this._suggestionTimer1 = setTimeout(() => {
      this.setData({
        searchInputFocused: false,
        showSearchSuggestions: false,
      });
      console.log("搜索框聚焦状态已设置为false");
    }, 500);
  },

  onSuggestionTouchMove() {
    //清除定时器
    clearTimeout(this._suggestionTimer2);
    clearTimeout(this._suggestionTimer1);
    //不滑动 5 秒后隐藏建议，只要在滑动就不隐藏
    this._suggestionTimer2 = setTimeout(() => {
      this.setData({
        searchInputFocused: false,
        showSearchSuggestions: false,
      });
    }, 2000);
  },

  /**
   * 搜索框点击事件
   */
  onSearchClick() {
    // 点击搜索框时，如果有关键词就显示建议
    if (this.data.searchKeyword.trim().length > 0) {
      this.loadSearchSuggestions(this.data.searchKeyword.trim());
    }
  },

  // 重复的方法已移除

  /* 已删除 exitSearchMode 方法 - 新交互流程不需要退出搜索模式 */

  /**
   * 加载搜索建议
   */
  async loadSearchSuggestions(keyword) {
    if (this.data.loadingSuggestions) return;

    try {
      this.setData({ loadingSuggestions: true });

      let suggestions = [];

      // 1. 优先从缓存的服装名称中筛选
      if (this.data.allClothingNames && this.data.allClothingNames.length > 0) {
        suggestions = this.data.allClothingNames.filter(
          (item) =>
            item.name && item.name.toLowerCase().includes(keyword.toLowerCase())
        ); // 最多20个建议

        console.log(`从缓存中筛选到 ${suggestions.length} 个匹配的服装名称`);
      }

      // 2. 如果缓存数据不足，从当前库存数据中补充
      if (
        suggestions.length < 5 &&
        this.data.inventoryList &&
        this.data.inventoryList.length > 0
      ) {
        const seenNames = new Set(suggestions.map((s) => s.name));

        this.data.inventoryList.forEach((item) => {
          if (suggestions.length >= 8) return; // 已达到上限

          // 检查包裹中的服装名称
          if (item.contents && item.contents.length > 0) {
            item.contents.forEach((content) => {
              if (suggestions.length >= 8) return;

              const clothingName = content.name;
              if (
                clothingName &&
                clothingName.toLowerCase().includes(keyword.toLowerCase()) &&
                !seenNames.has(clothingName)
              ) {
                seenNames.add(clothingName);
                suggestions.push({
                  name: clothingName,
                  warehouse_count: 1,
                  total_packages: 1,
                  total_pieces: content.current_quantity || 0,
                });
              }
            });
          }

          // 检查产品名称
          const productName = item.product_name;
          if (
            suggestions.length < 8 &&
            productName &&
            productName.toLowerCase().includes(keyword.toLowerCase()) &&
            !seenNames.has(productName)
          ) {
            seenNames.add(productName);
            suggestions.push({
              name: productName,
              warehouse_count: 1,
              total_packages: 1,
              total_pieces: item.total_quantity || 0,
            });
          }
        });

        console.log(`从当前库存数据中补充了建议，总数: ${suggestions.length}`);
      }

      // 3. 如果仍然不足且没有缓存，尝试从API获取（作为备用方案）
      if (
        suggestions.length < 3 &&
        (!this.data.allClothingNames || this.data.allClothingNames.length === 0)
      ) {
        try {
          const response = await Api.searchWarehouseInventory({
            product_name: keyword,
            limit: 10,
          });

          if (response.data.code === 200) {
            const searchData = response.data.data?.list || [];
            const seenNames = new Set(suggestions.map((s) => s.name));

            searchData.forEach((item) => {
              const clothingName = item.product_name || item.clothing_name;
              if (
                clothingName &&
                clothingName.toLowerCase().includes(keyword.toLowerCase()) &&
                !seenNames.has(clothingName)
              ) {
                seenNames.add(clothingName);
                suggestions.push({
                  name: clothingName,
                  warehouse_count: 1,
                });
              }
            });
          }
        } catch (apiError) {
          console.warn("API搜索建议失败，使用本地数据:", apiError);
        }
      }

      this.setData({
        searchSuggestions: suggestions, // 不再限制数量
        showSearchSuggestions:
          suggestions.length > 0 && this.data.searchInputFocused,
      });
    } catch (error) {
      console.error("加载搜索建议失败:", error);
      // 即使出错也要隐藏加载状态
      this.setData({
        showSearchSuggestions: false,
        searchSuggestions: [],
      });
    } finally {
      this.setData({ loadingSuggestions: false });
    }
  },

  /**
   * 点击搜索建议项
   */
  onSuggestionTap(e) {
    const suggestion = e.currentTarget.dataset.suggestion;
    this.setData({
      searchKeyword: suggestion.name,
      showSearchSuggestions: false,
    });

    // 自动执行搜索
    this.onSearch();
  },

  /**
   * 执行搜索 - 新的交互流程：直接显示所有有库存仓库的卡片
   */
  async onSearch() {
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      wx.showToast({
        title: "请输入服装名称",
        icon: "none",
      });
      return;
    }

    try {
      this.setData({ searchLoading: true });
      console.log("开始搜索:", keyword);

      const response = await Api.searchWarehouseInventory({
        product_name: keyword,
      });

      if (response.data.code === 200) {
        const searchData = response.data.data?.list || [];
        console.log("搜索结果:", searchData);

        // 按仓库分组处理搜索结果
        const groupedResults = this.groupSearchResultsByWarehouseSimple(searchData);

        if (groupedResults.length > 0) {
          // 新的交互流程：直接显示所有仓库的库存卡片
          const allWarehouseInventory = [];

          groupedResults.forEach((group) => {
            group.inventory.forEach((item) => {
              // 为每个库存项添加仓库信息
              const inventoryWithWarehouse = {
                ...item,
                warehouse_info: group.warehouse,
                // 如果有待出库清单，计算最终库存状态
                ...this.calculateInventoryStateForWarehouse(item, group.warehouse.warehouse_id)
              };
              allWarehouseInventory.push(inventoryWithWarehouse);
            });
          });

          this.setData({
            isSearchMode: true,
            searchResults: groupedResults,
            inventoryList: allWarehouseInventory,
          });

        } else {
          this.setData({
            isSearchMode: true,
            searchResults: [],
            inventoryList: [],
          });

          wx.showToast({
            title: "未找到相关库存",
            icon: "none",
          });
        }
      } else {
        wx.showToast({
          title: response.data.message || "搜索失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("搜索失败:", error);
      wx.showToast({
        title: "搜索失败",
        icon: "none",
      });
    } finally {
      this.setData({ searchLoading: false });
    }
  },

  /**
   * 计算指定仓库的库存状态（考虑待出库清单）
   */
  calculateInventoryStateForWarehouse(inventoryItem, warehouseId) {
    if (!this.data.outboundList || this.data.outboundList.length === 0) {
      return inventoryItem;
    }

    // 查找该仓库的待出库清单
    const warehouseOutbounds = this.data.outboundList.filter(
      (item) => item.warehouse_id === warehouseId &&
                item.classification_code === inventoryItem.classification_code
    );

    if (warehouseOutbounds.length === 0) {
      return inventoryItem;
    }

    // 使用现有的计算逻辑
    const finalState = this.calculateFinalInventoryStateNew([inventoryItem], warehouseOutbounds);
    return finalState[0] || inventoryItem;
  },

  /**
   * 重新计算搜索结果的库存显示（新交互流程）
   */
  recalculateSearchInventoryDisplay() {
    if (!this.data.isSearchMode || !this.data.inventoryList || this.data.inventoryList.length === 0) {
      return;
    }

    console.log("重新计算搜索结果的库存显示");

    const updatedInventoryList = this.data.inventoryList.map((inventoryItem) => {
      if (inventoryItem.warehouse_info) {
        return this.calculateInventoryStateForWarehouse(inventoryItem, inventoryItem.warehouse_info.warehouse_id);
      }
      return inventoryItem;
    });

    this.setData({
      inventoryList: updatedInventoryList,
    });

    console.log("搜索结果库存显示重新计算完成");
  },

  /**
   * 按仓库分组搜索结果 - 简化版本，使用classification_code，按仓库名称升序排序
   */
  groupSearchResultsByWarehouseSimple(searchData) {
    const warehouseMap = new Map();

    searchData.forEach((item) => {
      const warehouseId = item.warehouse_id;
      if (!warehouseMap.has(warehouseId)) {
        // 从原始仓库列表中查找仓库信息
        const originalWarehouse = this.data.originalWarehouseList.find(
          (w) => w.warehouse_id === warehouseId
        );

        warehouseMap.set(warehouseId, {
          warehouse: {
            warehouse_id: warehouseId,
            name:
              originalWarehouse?.name ||
              item.warehouse_name ||
              `仓库${warehouseId}`,
            address:
              originalWarehouse?.address ||
              item.warehouse_address ||
              "未知地址",
          },
          inventory: [],
        });
      }

      // 直接使用新的数据处理逻辑
      warehouseMap.get(warehouseId).inventory.push(item);
    });

    // 对每个仓库的数据使用新的处理方法
    for (const [warehouseId, warehouseData] of warehouseMap) {
      warehouseData.inventory = this.processClassificationCodeInventory(
        warehouseData.inventory,
        warehouseId
      );
    }

    // 转换为数组并按仓库名称升序排序
    const groupedResults = Array.from(warehouseMap.values());
    groupedResults.sort((a, b) => {
      const nameA = a.warehouse.name || '';
      const nameB = b.warehouse.name || '';
      return nameA.localeCompare(nameB);
    });

    return groupedResults;
  },

  /**
   * 重置搜索 - 新交互流程：清空搜索结果，回到初始状态
   */
  async onResetSearch() {
    console.log("=== 重置搜索开始 ===");

    this.setData({
      searchKeyword: "",
      isSearchMode: false,
      searchResults: [],
      inventoryList: [], // 清空库存列表，回到初始状态
      // 清空搜索建议相关状态
      showSearchSuggestions: false,
      searchSuggestions: [],
      searchInputFocused: false,
    });

    console.log("重置搜索状态已更新");

    // 新交互流程：不需要自动加载仓库库存，等待用户搜索
    console.log("准备聚焦搜索输入框");
    // 自动聚焦到搜索输入框
    this.focusSearchInput();
  },

  /**
   * 聚焦到搜索输入框
   */
  focusSearchInput() {
    console.log("=== 开始聚焦搜索输入框 ===");

    // 先设置为false，然后设置为true，确保触发聚焦
    this.setData(
      {
        searchInputFocused: false,
      },
      () => {
        // 延迟一小段时间确保状态更新完成
        setTimeout(() => {
          this.setData(
            {
              searchInputFocused: true,
            },
            () => {
              console.log("搜索输入框聚焦状态已设置为true");
            }
          );
        }, 50);
      }
    );
  },

  /* 已删除 recalculateCurrentWarehouseInventory 方法 - 新交互流程不再需要 */

  /**
   * 清空购物车
   */
  clearCart() {
    wx.showModal({
      title: "确认清空",
      content: "确定要清空待出库清单吗？",
      success: (res) => {
        if (res.confirm) {
          // 重置库存显示到原始状态
          const resetInventoryList = this.data.inventoryList.map((item) => ({
            ...item,
            availablePackageCount: item.package_count,
            availableTotalQuantity: item.total_quantity,
            selectedPackageQuantity: 0,
            calculatedPackageQuantity:
              item.contents.length > 1 ? "0.00" : undefined,
            contents: item.contents.map((content) => ({
              ...content,
              available_quantity: content.current_quantity,
              selectedQuantity: 0,
            })),
          }));
          this.setData({ inventoryList: resetInventoryList });

          this.setData({
            outboundList: [],
            cartTotalPackages: 0,
            cartTotalPieces: 0,
            showCart: false,
          });

          // 清除临时数据
          this.clearTempData();

          wx.showToast({
            title: "已清空",
            icon: "success",
          });
        }
      },
    });
  },

  // ==================== 搜索缓存管理 ====================

  /**
   * 初始化搜索选项缓存
   */
  async initializeSearchCache() {
    try {
      // 检查缓存是否有效
      if (SearchCache.isCacheValid()) {
        // 使用缓存数据
        const cachedNames = SearchCache.getCachedClothingNames();
        if (cachedNames && cachedNames.length > 0) {
          this.setData({
            allClothingNames: cachedNames,
          });
          console.log(
            `搜索缓存：使用缓存数据，包含 ${cachedNames.length} 个服装名称`
          );
          return;
        }
      }

      // 缓存无效或不存在，从API获取数据
      await this.loadClothingNamesFromAPI();
    } catch (error) {
      console.error("初始化搜索缓存失败:", error);
    }
  },

  /**
   * 从API加载服装名称并缓存
   */
  async loadClothingNamesFromAPI() {
    if (this.data.cacheLoading) return;

    try {
      this.setData({ cacheLoading: true });
      console.log("搜索缓存：从API加载服装名称数据");

      const response = await Api.getClothingNamesForSearch();
      console.log("API响应clothingNamesForSearch:", response);
      if (response.data && response.data.code === 200) {
        const clothingNames = response.data.data.list || [];

        // 缓存数据
        SearchCache.setCachedClothingNames(clothingNames);

        // 更新页面数据
        this.setData({
          allClothingNames: clothingNames,
        });

        console.log(
          `搜索缓存：成功加载并缓存 ${clothingNames.length} 个服装名称`
        );
      } else {
        console.warn("搜索缓存：API返回数据格式异常", response);
      }
    } catch (error) {
      console.error("搜索缓存：从API加载服装名称失败", error);
    } finally {
      this.setData({ cacheLoading: false });
    }
  },

  // ==================== 临时数据管理 ====================

  /**
   * 检查并恢复临时数据
   */
  checkAndRestoreTempData() {
    console.log("=== 检查临时数据恢复 ===");

    // 首先检查是否是出库确认后的状态
    const app = getApp();
    const isAfterOutboundConfirm =
      app.globalData && app.globalData.shouldClearOutboundList;
    console.log("是否为出库确认后状态:", isAfterOutboundConfirm);

    if (isAfterOutboundConfirm) {
      console.log("出库确认后状态，跳过临时数据恢复检查");
      return;
    }

    const hasData = TempStorage.has(TempStorage.STORAGE_KEYS.OUTBOUND_CART);
    console.log("是否有有效的临时数据:", hasData);

    if (hasData) {
      // 获取临时数据进行验证
      const tempData = TempStorage.getOutboundCart();
      console.log("临时数据内容:", tempData);

      if (tempData && tempData.length > 0) {
        console.log("显示恢复对话框，临时数据包含", tempData.length, "项");
        TempStorage.showRestoreDialog(
          "outbound",
          () => {
            // 确认恢复
            console.log("用户选择恢复数据");
            this.restoreTempData();
          },
          () => {
            // 取消恢复，清除数据
            console.log("用户选择清除数据");
            TempStorage.clearOutboundCart();
          }
        );
      } else {
        console.log("临时数据为空或无效，自动清除");
        TempStorage.clearOutboundCart();
      }
    } else {
      console.log("没有临时数据，跳过恢复流程");
    }
  },

  /**
   * 恢复临时数据
   */
  restoreTempData() {
    const savedOutboundList = TempStorage.getOutboundCart();
    if (savedOutboundList && savedOutboundList.length > 0) {
      // 修复：确保恢复的数据包含所有必要的字段
      const fixedOutboundList = savedOutboundList.map(item => {
        if (!item.display_name && item.contents) {
          // 重新生成显示名称
          const outboundContents = item.contents.filter(c => c.outbound_quantity > 0);
          const clothingNames = outboundContents.map(c => c.name || c.clothing_name || '未知服装');
          item.display_name = clothingNames.join(' + ');
          item.outbound_clothing_count = outboundContents.length;
        }
        return item;
      });

      this.setData({
        outboundList: fixedOutboundList,
      });

      // 重新计算购物车统计
      this.updateCartStatistics(fixedOutboundList);

      // 重新计算库存显示，确保正确扣除已添加到出库清单中的库存数量
      const finalInventoryList = this.calculateFinalInventoryStateNew(
        this.data.inventoryList,
        savedOutboundList
      );
      this.setData({ inventoryList: finalInventoryList });

      wx.showToast({
        title: "数据已恢复",
        icon: "success",
      });

      console.log("出库数据已恢复，库存显示已同步:", savedOutboundList);
    } else {
      // 如果没有有效的临时数据，清除临时存储并提示用户
      TempStorage.clearOutboundCart();
      wx.showToast({
        title: "没有可恢复的数据",
        icon: "none",
      });
      console.log("没有有效的临时数据，已清除临时存储");
    }
  },

  /**
   * 保存临时数据
   */
  saveTempData() {
    const { outboundList } = this.data;
    if (outboundList && outboundList.length > 0) {
      TempStorage.saveOutboundCart(outboundList, {
        page: "outbound",
        selectedWarehouse: this.data.selectedWarehouse,
      });
      console.log("临时数据已保存，包含", outboundList.length, "项");
    } else {
      // 如果出库清单为空，清除临时数据
      TempStorage.clearOutboundCart();
      console.log("出库清单为空，已清除临时数据");
    }
  },

  /**
   * 清除临时数据（操作完成后调用）
   */
  clearTempData() {
    TempStorage.clearOutboundCart();
  },
});
