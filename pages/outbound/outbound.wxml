<!-- pages/outbound/outbound.wxml -->
<view class="outbound-container">
  <!-- 加载状态 -->
  <van-loading wx:if="{{loading}}" type="spinner" color="#1989fa" size="24px" vertical>
    加载中...
  </van-loading>
  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 搜索卡片 - 参照仓库日志样式 -->
    <view class="search-section">
      <view class="search-header">
        <view class="search-form">
          <!-- 服装名称输入框 -->
          <view class="search-form-item search-input-container">
            <input id="search-input" class="search-form-input" value="{{searchKeyword}}" placeholder="输入服装名称搜索库存" focus="{{searchInputFocused}}" bindinput="onSearchInput" bindfocus="onSearchFocus" bindblur="onSearchBlur" bindtap="onSearchClick" />
            <!-- 智能下拉提示 -->
            <view wx:if="{{showSearchSuggestions}}" class="search-suggestions">
              <view wx:if="{{loadingSuggestions}}" class="suggestion-loading">
                <van-loading type="spinner" size="16px" />
                <text>搜索中...</text>
              </view>
              <view wx:else>
                <view wx:for="{{searchSuggestions}}" wx:key="name" class="suggestion-item" data-suggestion="{{item}}" bindtap="onSuggestionTap" bindtouchmove="onSuggestionTouchMove">
                  <view class="suggestion-name">{{item.name}}</view>
                  <van-icon name="arrow" size="14px" color="#999" />
                </view>
              </view>
            </view>
          </view>
          <!-- 搜索按钮 -->
          <view class="search-form-item">
            <van-button size="small" type="primary" bind:click="onSearch" loading="{{searchLoading}}" disabled="{{searchLoading}}" custom-class="search-form-btn">
              搜索
            </van-button>
          </view>
          <!-- 重置按钮 -->
          <view class="search-form-item">
            <van-button size="small" bind:click="onResetSearch" custom-class="reset-form-btn">
              重置
            </van-button>
          </view>
        </view>
      </view>
    </view>
    <!-- 新的交互流程：直接显示库存卡片列表 -->
    <view class="inventory-layout">
      <!-- 库存明细列表 -->
      <view class="inventory-panel">
        <view wx:if="{{inventoryList.length === 0}}" class="empty-state">
          <van-empty description="{{isSearchMode ? '请搜索服装查看库存' : '暂无库存数据'}}" />
        </view>
        <view wx:else class="details-list">
          <view wx:for="{{inventoryList}}" wx:for-index="inventoryIndex" wx:key="unique_id" class="detail-item">
            <!-- 单货物包裹卡片 -->
            <view wx:if="{{item.contents.length === 1}}" class="item-content single-item-card">
              <!-- 仓库信息标题（新增） -->
              <view wx:if="{{item.warehouse_info}}" class="warehouse-header">
                <view class="warehouse-name">{{item.warehouse_info.name}}</view>
                <view class="warehouse-address">{{item.warehouse_info.address}}</view>
              </view>
              <!-- 主要信息横向布局 -->
              <view class="item-main-info">
                <!-- 左侧：服装信息 -->
                <view class="clothing-info">
                  <view class="clothing-name clickable" style="color: #000" data-content="{{item.contents[0]}}" bindtap="onClothingNameTap">
                    {{item.contents[0].name || '未知服装'}}
                  </view>
                </view>
                <!-- 右侧：库存信息 -->
                <view class="stock-info">
                  <view class="stock-formula">
                    {{item.availableTotalQuantity !== undefined ? item.availableTotalQuantity : item.total_quantity || 0}}件   ({{item.contents[0].original_quantity || 0}}件/包)
                  </view>
                </view>
              </view>
              <!-- 操作区域：库存数量、步进器、出库按钮在同一排 -->
              <view class="item-actions-row" wx:if="{{(item.availablePackageCount !== undefined ? item.availablePackageCount : item.package_count) > 0}}">
                <!-- 固定位置的数量显示和控制组 -->
                <view class="stock-quantity-group">
                  <view class="stock-display">
                    <view class="stock-value">
                      {{item.availablePackageCount !== undefined ? item.availablePackageCount : item.package_count}}包
                    </view>
                  </view>
                  <view class="quantity-control">
                    <van-stepper value="{{item.selectedPackageQuantity || 0}}" min="0" max="{{item.availablePackageCount !== undefined ? item.availablePackageCount : item.package_count}}" step="0.5" data-inventory-index="{{inventoryIndex}}" bind:change="onSingleItemQuantityChange" />
                  </view>
                </view>
                <!-- 出库按钮区域 -->
                <view class="outbound-button-area">
                  <van-button wx:if="{{item.selectedPackageQuantity && item.selectedPackageQuantity > 0}}" type="primary" size="small" data-inventory-index="{{inventoryIndex}}" bind:click="onSingleItemOutbound">
                    出库
                  </van-button>
                </view>
              </view>
              <!-- 提示信息 -->
              <view class="item-tips" wx:if="{{(item.availablePackageCount !== undefined ? item.availablePackageCount : item.package_count) <= 0}}">
                <view class="empty-tip">暂无库存</view>
              </view>
            </view>
            <!-- 多货物包裹卡片 -->
            <view wx:else class="item-content">
              <!-- 仓库信息标题（新增） -->
              <view wx:if="{{item.warehouse_info}}" class="warehouse-header">
                <view class="warehouse-name">{{item.warehouse_info.name}}</view>
                <view class="warehouse-address">{{item.warehouse_info.address}}</view>
              </view>
              <!-- 货物列表 -->
              <view class="multi-item-list">
                <view wx:for="{{item.contents}}" wx:for-item="content" wx:for-index="contentIndex" wx:key="sku" class="multi-item-row">
                  <!-- 左侧：服装信息 -->
                  <view class="multi-item-info">
                    <view class="clothing-name clickable" style="color: #000" data-content="{{content}}" bindtap="onClothingNameTap">
                      {{content.name}}
                    </view>
                    <view class="multi-item-quantity">
                      {{content.available_quantity !== undefined ? content.available_quantity : content.current_quantity}}件
                    </view>
                  </view>
                  <!-- 右侧：数字输入框 -->
                  <view class="multi-item-input">
                    <van-field value="{{content.selectedQuantity || 0}}" type="number" placeholder="0" data-inventory-index="{{inventoryIndex}}" data-content-index="{{contentIndex}}" bind:change="onMultiItemQuantityChange" input-class="multi-input-field" />
                  </view>
                </view>
              </view>
              <!-- 出货包数计算和出库按钮 -->
              <view class="multi-item-footer">
                <view class="package-calculation">
                  <view class="calculation-display">
                    出库包数: {{item.calculatedPackageQuantity || '0'}}包
                  </view>
                </view>
                <van-button wx:if="{{item.calculatedPackageQuantity && item.calculatedPackageQuantity > 0}}" type="primary" size="small" data-inventory-index="{{inventoryIndex}}" bind:click="onMultiItemOutbound">
                  出库
                </van-button>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 底部固定操作栏 -->
  <view class="bottom-fixed-bar">
    <view class="bottom-actions">
      <!-- 待出库清单按钮 -->
      <view class="action-item">
        <van-button type="info" size="large" bind:click="toggleCart" class="action-button" disabled="{{!outboundList || outboundList.length === 0}}">
          <van-icon name="shopping-cart-o" />
          待出库 ({{cartTotalPackages}}包)
        </van-button>
      </view>
      <!-- 核对按钮 -->
      <view class="action-item">
        <van-button type="success" size="large" bind:click="confirmOutbound" class="action-button" disabled="{{!outboundList || outboundList.length === 0}}">
          <van-icon name="passed" />
          核对
        </van-button>
      </view>
    </view>
  </view>
  <!-- 待出库清单浮窗 -->
  <van-popup show="{{showCart}}" position="bottom" bind:close="toggleCart" custom-style="height: 80vh;">
    <view class="cart-popup">
      <view class="popup-header">
        <view class="popup-title">待出库清单</view>
        <view class="header-actions">
          <view class="total-info">总计：{{cartTotalPackages}}包 / {{cartTotalPieces}}件</view>
          <van-button type="danger" size="small" bind:click="clearCart">清空</van-button>
        </view>
      </view>
      <view class="cart-content">
        <view wx:if="{{outboundList.length === 0}}" class="empty-cart">
          <van-empty description="待出库清单为空" />
        </view>
        <view wx:else>
          <!-- 新的列表形式显示 -->
          <view class="outbound-list">
            <view wx:for="{{outboundList}}" wx:for-index="index" wx:key="id" class="outbound-item">
              <!-- 序号 -->
              <view class="item-index">{{index + 1}}</view>

              <!-- 仓库名称 -->
              <view class="item-warehouse">{{item.warehouse_name}}</view>

              <!-- 服装名称 - 统一显示逻辑 -->
              <view class="item-clothing">
                <view class="clothing-name clickable" style="color: #000" bindtap="onOutboundItemNameTap" data-index="{{index}}">
                  {{item.display_name}}
                </view>
              </view>

              <!-- 出库信息 -->
              <view class="item-info">
                <view wx:if="{{item.package_type === 'single'}}" class="outbound-formula">
                  {{item.package_count}}包 × {{item.contents[0].original_quantity}}件/包 = {{item.total_pieces}}件
                </view>
                <view wx:else class="outbound-formula">
                  {{item.package_count}}包 = {{item.total_pieces}}件
                </view>
              </view>

              <!-- 删除按钮 -->
              <view class="item-actions">
                <van-icon name="close" data-outbound-id="{{item.id}}" bind:click="removeFromCartNew" class="remove-icon" />
              </view>
            </view>


          </view>
        </view>
      </view>
    </view>
  </van-popup>
  <!-- 服装信息弹窗 -->
  <van-popup show="{{ showClothingInfo }}" round position="center" custom-style="width: 90%; max-width: 600rpx; border-radius: 16rpx;" bind:close="closeClothingInfo">
    <view class="clothing-popup-container">
      <z-clothing-info-card clothingInfo="{{ selectedClothingInfo.is_oem ? null : selectedClothingInfo }}" oemClothingInfo="{{ selectedClothingInfo.is_oem ? selectedClothingInfo : null }}" isOem="{{ selectedClothingInfo.is_oem }}" />
    </view>
  </van-popup>
  <!-- 加载状态 -->
  <van-loading wx:if="{{ loading }}" type="spinner" />
</view>