// pages/login/login.js
import Api from "../../utils/api.js"; // 引入封装好的API文件

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userName: "",
    userPwd: "",
    isLogin: false,
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    // 检查是否已有token
    const token = wx.getStorageSync("tokenKey");
    if (token) {
      // 已登录，跳转到仓库管理页面
      this.setData({ isLogin: true });
      setTimeout(() => {
        wx.switchTab({
          url: "/pages/warehouses/warehouses",
        });
      }, 1000);
      return;
    }

    // 尝试基于微信openId的自动登录
    try {
      console.log("尝试基于微信openId的自动登录...");
      const openIdResult = await this.getOpenid();

      if (openIdResult && openIdResult.data && openIdResult.data.data && openIdResult.data.data.openid) {
        const openId = openIdResult.data.data.openid;
        console.log("获取到微信openId:", openId);

        // 尝试使用openId登录
        const autoLoginResult = await Api.login({
          wxOpenId: openId
        });

        if (autoLoginResult && autoLoginResult.data && autoLoginResult.data.code === 200) {
          // 自动登录成功
          console.log("基于openId自动登录成功");
          wx.setStorageSync("tokenKey", autoLoginResult.data.data.token);

          this.setData({ isLogin: true });
          wx.showToast({
            title: "自动登录成功",
            icon: "success",
          });

          setTimeout(() => {
            wx.switchTab({
              url: "/pages/warehouses/warehouses",
            });
          }, 1000);
          return;
        } else {
          console.log("基于openId自动登录失败，需要手动登录");
          // 保存openId，用于首次登录时关联
          wx.setStorageSync("tempOpenId", openId);
        }
      }
    } catch (error) {
      console.error("自动登录过程出错:", error);
    }

    // 显示登录界面
    this.setData({ isLogin: false });
  },

  /**
   * 获取微信OpenID
   */
  async getOpenid() {
    try {
      const { code } = await wx.login();
      const params = {
        code: code,
      };
      return await Api.getOpenId(params);
    } catch (error) {
      console.error("获取微信授权码失败:", error);
      throw error;
    }
  },

  /**
   * 表单验证
   */
  validateLoginForm() {
    // 用户名验证
    if (!this.data.userName || this.data.userName.trim() === "") {
      wx.showToast({
        title: "请输入用户名",
        icon: "none",
      });
      return false;
    }

    // 密码验证
    if (!this.data.userPwd || this.data.userPwd.trim() === "") {
      wx.showToast({
        title: "请输入密码",
        icon: "none",
      });
      return false;
    }

    if (this.data.userPwd.length < 6) {
      wx.showToast({
        title: "密码长度不能少于6个字符",
        icon: "none",
      });
      return false;
    }

    return true;
  },

  /**
   * 登录处理
   */
  async onLogin() {
    if (this.data.loading) return;

    // 表单验证
    if (!this.validateLoginForm()) {
      return;
    }

    this.setData({ loading: true });

    try {
      // 获取临时保存的openId
      const tempOpenId = wx.getStorageSync("tempOpenId");

      // 使用用户名密码登录，同时传递openId用于关联
      const loginParams = {
        userName: this.data.userName.trim(),
        userPwd: this.data.userPwd,
      };

      // 如果有临时openId，使用微信小程序登录接口（支持openId关联）
      let loginRes;
      if (tempOpenId) {
        console.log("首次登录，关联微信openId:", tempOpenId);
        loginRes = await Api.login({
          userName: this.data.userName.trim(),
          userPwd: this.data.userPwd,
          wxOpenId: tempOpenId
        });
      } else {
        // 没有openId，使用传统登录方式
        loginRes = await Api.userLogin(loginParams);
      }

      // 登录成功，后端返回token和用户信息
      if (loginRes && loginRes.data && (loginRes.data.token || (loginRes.data.data && loginRes.data.data.token))) {
        // 保存token（兼容不同的响应格式）
        const token = loginRes.data.token || loginRes.data.data.token;
        wx.setStorageSync("tokenKey", token);

        // 清除临时openId
        if (tempOpenId) {
          wx.removeStorageSync("tempOpenId");
          console.log("登录成功，已关联微信openId，下次可自动登录");
        }

        wx.showToast({
          title: "登录成功",
          icon: "success",
        });

        // 跳转到仓库管理页面
        setTimeout(() => {
          wx.switchTab({
            url: "/pages/warehouses/warehouses",
          });
        }, 1000);
      } else {
        wx.showToast({
          title: "登录失败，请检查用户名和密码",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("登录失败:", error);

      // 处理后端返回的错误信息
      let errorMessage = "登录失败，请重试";
      if (error.data && error.data.message) {
        errorMessage = error.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      wx.showToast({
        title: errorMessage,
        icon: "none",
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  /**
   * 输入用户名
   */
  onUserNameInput(e) {
    this.setData({
      userName: e.detail.value.trim(),
    });
  },

  /**
   * 输入用户名（旧方法，保持兼容）
   */
  inputUserName(e) {
    this.setData({
      userName: e.detail.value,
    });
  },

  /**
   * 输入密码
   */
  inputPassword(e) {
    this.setData({
      userPwd: e.detail.value,
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 跳转到注册页面
   */
  goToRegister() {
    wx.navigateTo({
      url: "/pages/register/register",
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {},
});
