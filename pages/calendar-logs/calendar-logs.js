// pages/calendar-logs/calendar-logs.js
import Api from "../../utils/api.js";

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 查询参数
    queryParams: {
      start_date: '',
      end_date: '',
      warehouse_id: '',
      clothing_id: '',
      oem_clothing_id: '',
      operation_type: 'outbound',
      isOem: false
    },

    // 选中的操作类型
    selectedOperationType: 'outbound', // 默认选中出库

    // 选中的数据展示选项卡
    selectedDataTab: 'detail', // 默认选中明细

    // 日志数据
    logsList: [],
    loadingLogs: false,
    hasMoreLogs: true,
    logsPage: 1,
    lastApiResponse: null,

    // 汇总数据
    warehouseSummaryList: [], // 按仓库汇总
    clothingSummaryList: [], // 按服装汇总

    // 页面来源信息
    pageSource: '', // 'warehouse-main' | 'warehouse-detail' | 'clothing-detail'

    // 操作类型定义
    operationTypes: {
      'inbound': { name: '入库', value: 'inbound' },
      'outbound': { name: '出库', value: 'outbound' },
      'transfer': { name: '移库', value: 'transfer' },
      'inventory': { name: '盘存', value: 'inventory' }
    },

    // 页面标题信息
    pageTitle: '日志查询',

    // 页面来源相关信息
    warehouseName: '', // 仓库名称（从仓库详情页进入时显示）
    clothingName: '', // 服装名称（从服装详情页进入时显示）
    loadingSourceInfo: false, // 加载来源信息状态

    // 服装信息卡片显示
    showClothingCard: false,
    selectedClothingInfo: null,
    isOemClothing: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

    // 解析查询参数
    const queryParams = {
      start_date: options.start_date || '',
      end_date: options.end_date || '',
      warehouse_id: options.warehouse_id || '',
      clothing_id: options.clothing_id || '',
      oem_clothing_id: options.oem_clothing_id || '',
      operation_type: options.operation_type || 'outbound',
      isOem: options.isOem === 'true' // 解析isOem参数
    };

    // 判断页面来源
    let pageSource = 'warehouse-main'; // 默认为仓库主页面
    if (queryParams.warehouse_id && !queryParams.clothing_id && !queryParams.oem_clothing_id) {
      pageSource = 'warehouse-detail';
    } else if (queryParams.clothing_id || queryParams.oem_clothing_id) {
      pageSource = 'clothing-detail';
    }

    // 设置页面标题和日期范围文本
    let pageTitle = '日志查询';

    if (queryParams.start_date && queryParams.end_date) {
      if (queryParams.start_date === queryParams.end_date) {
        pageTitle = `${queryParams.start_date} `;
      } else {
        pageTitle = `${queryParams.start_date} 至 ${queryParams.end_date} `;
      }
    }

    // 设置选中的操作类型
    const selectedOperationType = queryParams.operation_type || 'outbound';

    this.setData({
      queryParams,
      selectedOperationType,
      pageTitle,
      pageSource
    });

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: pageTitle
    });

    // 根据页面来源加载相关信息
    this.loadSourceInfo();

    // 加载日志数据
    this.loadLogs(true);
  },

  /**
   * 加载来源信息（仓库名称或服装名称）
   */
  async loadSourceInfo() {
    const { pageSource, queryParams } = this.data;

    if (pageSource === 'warehouse-detail' && queryParams.warehouse_id) {
      // 从仓库详情页进入，获取仓库名称
      this.setData({ loadingSourceInfo: true });
      try {
        const response = await Api.getWarehouseDetail({ warehouse_id: queryParams.warehouse_id });

        if (response.data && response.data.code === 200) {
          const warehouseName = response.data.data.name || '未知仓库';
          this.setData({
            warehouseName: warehouseName
          });
        } else {
          console.error('获取仓库详情失败，响应码:', response.data?.code);
          this.setData({
            warehouseName: '获取失败'
          });
        }
      } catch (error) {
        console.error('获取仓库信息失败:', error);
        this.setData({
          warehouseName: '获取失败'
        });
      } finally {
        this.setData({ loadingSourceInfo: false });
      }
    } else if (pageSource === 'clothing-detail' && (queryParams.clothing_id || queryParams.oem_clothing_id)) {
      // 从服装详情页进入，获取服装名称
      this.setData({ loadingSourceInfo: true });
      try {
        let response;
        let clothingName = '';

        if (queryParams.oem_clothing_id || queryParams.isOem) {
          // 直接调用OEM服装API
          const oemClothingId = queryParams.oem_clothing_id || queryParams.clothing_id;
          response = await Api.getOemClothingInfo({ oem_clothing_id: oemClothingId });
          if (response.data && response.data.oem_clothing_name) {
            clothingName = response.data.oem_clothing_name;
          }
        } else if (queryParams.clothing_id) {
          // 直接调用普通服装API
          response = await Api.getClothingInfo({ clothing_id: queryParams.clothing_id });
          if (response.data && response.data.clothing_name) {
            clothingName = response.data.clothing_name;
          }
        }

        // 设置结果
        if (clothingName) {
          this.setData({
            clothingName: clothingName
          });
        } else {
          console.error('获取服装名称失败，API返回数据不完整');
          this.setData({
            clothingName: '获取失败'
          });
        }

      } catch (error) {
        console.error('获取服装信息失败:', error);
        this.setData({
          clothingName: '获取失败'
        });
      } finally {
        this.setData({ loadingSourceInfo: false });
      }
    }
  },

  /**
   * 加载日志数据
   */
  async loadLogs(reset = false) {
    if (this.data.loadingLogs || (!this.data.hasMoreLogs && !reset)) return;

    try {
      this.setData({ loadingLogs: true });

      const { queryParams, selectedOperationType } = this.data;
      // 优化：不再需要分页逻辑，直接一次性获取所有数据

      // 构建查询参数 - 优化：一次性获取所有数据
      const params = {
        page: 1, // 固定第一页
        limit: 1000, // 增大限制，一次性获取所有数据
        start_date: queryParams.start_date,
        end_date: queryParams.end_date,
        operation_type: selectedOperationType,
        log_type: 'detail' // 固定使用明细模式
      };

      // 如果有仓库ID，添加到参数中
      if (queryParams.warehouse_id) {
        params.warehouse_id = queryParams.warehouse_id;
      }

      // 如果有服装ID，添加到参数中
      if (queryParams.clothing_id) {
        params.clothing_id = queryParams.clothing_id;
      }

      // 如果有OEM服装ID，添加到参数中
      if (queryParams.oem_clothing_id) {
        params.oem_clothing_id = queryParams.oem_clothing_id;
      }

      const response = await Api.getWarehouseOperationLogsDetail(params);

      if (response.data.code === 200) {
        const newLogs = response.data.data.list || [];
        // 优化：由于一次性获取所有数据，不再需要分页逻辑
        const hasMore = false; // 一次性获取完毕，无更多数据

        // 对新数据进行排序
        const sortedNewLogs = this.sortLogsList(newLogs);
        const finalLogsList = sortedNewLogs; // 直接使用新数据，不需要合并

        this.setData({
          logsList: finalLogsList,
          hasMoreLogs: hasMore,
          logsPage: 2, // 设置为2，表示已加载完毕
          lastApiResponse: response.data.data
        });

        console.log(`日历查询优化：一次性加载了 ${finalLogsList.length} 条日志记录`);

        // 计算汇总数据
        this.calculateSummaryData(finalLogsList);
      } else {
        console.error('加载日志失败:', response.data.message);
        wx.showToast({
          title: response.data.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载日志异常:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({ loadingLogs: false });
    }
  },

  /**
   * 加载更多日志 - 优化：已一次性加载所有数据，无需更多加载
   */
  loadMoreLogs() {
    // 优化：由于已一次性获取所有数据，不再需要加载更多
    console.log('已一次性加载所有日志数据，无需更多加载');
    return;
  },

  /**
   * 数据排序方法
   */
  sortLogsList(logs) {
    return logs.sort((a, b) => {
      // 第一级排序：按仓库名称分组
      const warehouseA = a.warehouse_name || '';
      const warehouseB = b.warehouse_name || '';
      if (warehouseA !== warehouseB) {
        return warehouseA.localeCompare(warehouseB);
      }

      // 第二级排序：在同一仓库内，按服装名称分组
      const clothingA = a.product_name || a.clothing_name || '';
      const clothingB = b.product_name || b.clothing_name || '';
      if (clothingA !== clothingB) {
        return clothingA.localeCompare(clothingB);
      }

      // 第三级排序：按时间倒序排列（最新的记录在前）
      const dateA = new Date(a.created_at || a.date || 0);
      const dateB = new Date(b.created_at || b.date || 0);
      return dateB - dateA;
    });
  },

  /**
   * 操作类型切换事件
   */
  onOperationTypeChange(e) {
    const operationType = e.currentTarget.dataset.type;

    this.setData({
      selectedOperationType: operationType,
      logsList: [],
      logsPage: 1,
      hasMoreLogs: true,
      lastApiResponse: null,
      warehouseSummaryList: [],
      clothingSummaryList: []
    });

    this.loadLogs(true);
  },

  /**
   * 数据展示选项卡切换事件
   */
  onDataTabChange(e) {
    const dataTab = e.currentTarget.dataset.tab;
    this.setData({
      selectedDataTab: dataTab
    });
  },

  /**
   * 格式化包裹数显示
   */
  formatPackageNumber(num) {
    if (num === 0) return '0';

    // 如果是整数，直接显示整数
    if (num % 1 === 0) {
      return num.toString();
    }

    // 保留2位小数后检查
    const rounded = Math.round(num * 100) / 100;

    // 如果只有1位小数，显示1位小数
    if (rounded * 10 % 1 === 0) {
      return rounded.toFixed(1);
    }

    // 否则显示2位小数
    return rounded.toFixed(2);
  },

  /**
   * 计算汇总数据
   */
  calculateSummaryData(logsList) {
    // 按仓库汇总：仓库 -> 服装分类 -> 汇总数据
    const warehouseMap = {};
    // 按服装汇总：服装分类 -> 汇总数据
    const clothingMap = {};

    logsList.forEach(log => {
      const warehouseName = log.warehouse_name || '未知仓库';

      // 检查是否为混合包裹
      const isMixedPackage = log.contents_changes && log.contents_changes.length > 1;

      if (isMixedPackage) {
        // 混合包裹处理：展开显示每个子项目
        log.contents_changes.forEach(change => {
          const clothingName = change.product_name || change.clothing_name || '未知服装';
          const classificationCode = change.classification_code || clothingName;
          const quantityChange = change.quantity_change || 0;
          const packageTotalOriginalQuantity = log.package_total_original_quantity || 0;

          // 计算包裹数：quantity_change ÷ package_total_original_quantity
          let calculatedPackages = 0;
          if (packageTotalOriginalQuantity > 0) {
            calculatedPackages = quantityChange / packageTotalOriginalQuantity;
            calculatedPackages = Math.round(calculatedPackages * 100) / 100;
          }

          this.addToSummaryMaps(warehouseMap, clothingMap, {
            warehouseName,
            clothingName,
            classificationCode,
            quantityChange,
            calculatedPackages,
            sampleLog: change,
            isMixedItem: true,
            originalLog: log
          });
        });
      } else {
        // 单一包裹处理
        const changes = log.contents_changes && log.contents_changes.length > 0
          ? log.contents_changes
          : [log];

        changes.forEach(change => {
          const clothingName = change.product_name || change.clothing_name || log.product_name || log.clothing_name || '未知服装';
          const classificationCode = change.classification_code || log.classification_code || clothingName;
          const quantityChange = change.quantity_change || log.quantity_change || 0;
          const packageTotalOriginalQuantity = change.package_total_original_quantity || log.package_total_original_quantity || 0;

          // 计算包裹数：quantity_change ÷ package_total_original_quantity
          let calculatedPackages = 0;
          if (packageTotalOriginalQuantity > 0) {
            calculatedPackages = quantityChange / packageTotalOriginalQuantity;
            calculatedPackages = Math.round(calculatedPackages * 100) / 100;
          }

          this.addToSummaryMaps(warehouseMap, clothingMap, {
            warehouseName,
            clothingName,
            classificationCode,
            quantityChange,
            calculatedPackages,
            sampleLog: change.clothing_id || change.oem_clothing_id ? change : log,
            isMixedItem: false,
            originalLog: log
          });
        });
      }
    });

    // 转换按仓库汇总数据
    const warehouseSummaryList = Object.values(warehouseMap).map(warehouse => {
      const clothingList = Object.values(warehouse.clothing_items).map(clothing => ({
        ...clothing,
        // 使用新的格式化函数
        total_packages: this.formatPackageNumber(clothing.total_packages)
      }));

      return {
        warehouse_name: warehouse.warehouse_name,
        clothing_list: clothingList,
        // 使用新的格式化函数
        total_packages: this.formatPackageNumber(warehouse.total_packages)
      };
    }).sort((a, b) => a.warehouse_name.localeCompare(b.warehouse_name));

    // 转换按服装汇总数据
    const clothingSummaryList = Object.values(clothingMap).map(item => ({
      ...item,
      // 使用新的格式化函数
      total_packages: this.formatPackageNumber(item.total_packages)
    })).sort((a, b) => a.clothing_name.localeCompare(b.clothing_name));

    this.setData({
      warehouseSummaryList,
      clothingSummaryList
    });


  },

  /**
   * 从数据中提取服装标识信息的辅助方法
   */
  extractClothingIdentifiers(data) {
    if (!data) return {};

    let result = {
      sku: data.sku,
      clothing_id: data.clothing_id,
      oem_clothing_id: data.oem_clothing_id,
      classification_code: data.classification_code
    };

    // 如果顶层没有SKU，尝试从contents_changes中提取
    if (!result.sku && data.contents_changes && Array.isArray(data.contents_changes) && data.contents_changes.length > 0) {
      const firstChange = data.contents_changes[0];
      if (firstChange) {
        result.sku = result.sku || firstChange.sku;
        result.clothing_id = result.clothing_id || firstChange.clothing_id;
        result.oem_clothing_id = result.oem_clothing_id || firstChange.oem_clothing_id;
        result.classification_code = result.classification_code || firstChange.classification_code;
      }
    }

    // 过滤掉undefined值
    Object.keys(result).forEach(key => {
      if (result[key] === undefined) {
        delete result[key];
      }
    });

    return result;
  },



  /**
   * 添加数据到汇总映射中
   */
  addToSummaryMaps(warehouseMap, clothingMap, data) {
    const { warehouseName, clothingName, classificationCode, quantityChange, calculatedPackages, sampleLog, isMixedItem, originalLog } = data;

    // 按仓库汇总
    if (!warehouseMap[warehouseName]) {
      warehouseMap[warehouseName] = {
        warehouse_name: warehouseName,
        clothing_items: {},
        // 当前仓库的包裹总数的计算
        total_packages: 0 
      };
    }

    // 在仓库内按classification_code分组
    if (!warehouseMap[warehouseName].clothing_items[classificationCode]) {
      // 构建增强的sample_log，确保包含足够的信息
      const enhancedSampleLog = {
        ...sampleLog,
        classification_code: classificationCode,
        clothing_name: clothingName,
        product_name: clothingName,
        // 如果sampleLog中没有ID信息，尝试从originalLog中获取
        clothing_id: sampleLog.clothing_id || originalLog.clothing_id,
        oem_clothing_id: sampleLog.oem_clothing_id || originalLog.oem_clothing_id,
        sku: sampleLog.sku || originalLog.sku,
        // 保留原始日志信息以备用
        _original_log: originalLog
      };

      warehouseMap[warehouseName].clothing_items[classificationCode] = {
        classification_code: classificationCode,
        clothing_name: clothingName,
        total_pieces: 0,
        total_packages: 0,
        sample_log: enhancedSampleLog,
        is_mixed_item: isMixedItem,
        original_log: originalLog
      };
    }

    // 累加同一classification_code的数据
    warehouseMap[warehouseName].clothing_items[classificationCode].total_pieces += quantityChange;
    warehouseMap[warehouseName].clothing_items[classificationCode].total_packages += calculatedPackages;
    // 累加仓库的包裹总数
    warehouseMap[warehouseName].total_packages += calculatedPackages;

    // 按服装汇总
    if (!clothingMap[classificationCode]) {
      // 构建增强的sample_log，确保包含足够的信息
      const enhancedSampleLog = {
        ...sampleLog,
        classification_code: classificationCode,
        clothing_name: clothingName,
        product_name: clothingName,
        // 如果sampleLog中没有ID信息，尝试从originalLog中获取
        clothing_id: sampleLog.clothing_id || originalLog.clothing_id,
        oem_clothing_id: sampleLog.oem_clothing_id || originalLog.oem_clothing_id,
        sku: sampleLog.sku || originalLog.sku,
        // 保留原始日志信息以备用
        _original_log: originalLog
      };

      clothingMap[classificationCode] = {
        classification_code: classificationCode,
        clothing_name: clothingName,
        total_pieces: 0,
        total_packages: 0,
        sample_log: enhancedSampleLog,
        is_mixed_item: isMixedItem,
        original_log: originalLog
      };
    }

    // 累加同一classification_code的数据
    clothingMap[classificationCode].total_pieces += quantityChange;
    clothingMap[classificationCode].total_packages += calculatedPackages;
  },

  /**
   * 点击汇总视图中的服装名称
   */
  async onSummaryClothingNameTap(e) {
    await this.showClothingDetail(e, 'summary');
  },

  /**
   * 点击明细视图中的服装名称
   */
  async onClothingNameTap(e) {
    await this.showClothingDetail(e, 'detail');
  },

  /**
   * 统一的服装详情显示方法
   * @param {Object} e - 事件对象
   * @param {String} source - 数据源类型：'summary'(汇总视图) 或 'detail'(明细视图)
   */
  async showClothingDetail(e, source = 'detail') {
    const item = e.currentTarget.dataset.item;
    const change = e.currentTarget.dataset.change;

    // 根据数据源类型处理数据
    let targetData;
    if (source === 'summary') {
      // 汇总视图：优先使用sample_log，否则从原始数据构建
      if (item?.sample_log && typeof item.sample_log === 'object') {
        targetData = item.sample_log;
      } else {
        targetData = {
          classification_code: item?.classification_code,
          clothing_name: item?.clothing_name,
          product_name: item?.clothing_name,
          ...(item?.original_log || {})
        };

        // 从classification_code推断标识信息
        if (item?.classification_code && item.classification_code !== item.clothing_name) {
          if (item.classification_code.includes('_')) {
            targetData.sku = item.classification_code;
          } else {
            targetData.clothing_id = item.classification_code;
          }
        }
      }
    } else {
      // 明细视图：优先使用change数据，否则使用item数据
      targetData = change || item;
    }

    if (!targetData) {
      wx.showToast({
        title: '服装信息不完整',
        icon: 'none'
      });
      return;
    }

    // 提取服装标识信息
    const extractedIdentifiers = this.extractClothingIdentifiers(targetData);

    // 检查是否有任何可用的标识字段
    if (Object.keys(extractedIdentifiers).length === 0) {
      wx.showToast({
        title: '服装信息不完整',
        icon: 'none'
      });
      return;
    }

    // 合并提取的标识信息
    const enhancedTargetData = {
      ...targetData,
      ...extractedIdentifiers
    };

    // 显示加载状态
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      const clothingInfo = await this.fetchClothingInfo(enhancedTargetData);

      if (clothingInfo.data && typeof clothingInfo.data === 'object') {
        this.setData({
          selectedClothingInfo: clothingInfo.data,
          isOemClothing: clothingInfo.isOem,
          showClothingCard: true
        });
      } else {
        console.error('未获取到服装信息，所有API尝试都失败了');
        wx.showToast({
          title: '未找到该服装的详细信息',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('获取服装详情失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 获取服装详情信息
   * @param {Object} targetData - 包含服装标识信息的数据对象
   * @returns {Object} - 包含服装信息和类型的对象 {data, isOem}
   */
  async fetchClothingInfo(targetData) {
    let clothingInfo = null;
    let isOemClothing = false;

    // 优先使用clothing_id或oem_clothing_id
    if (targetData.oem_clothing_id) {
      isOemClothing = true;
      const response = await Api.getOemClothingInfo({ oem_clothing_id: targetData.oem_clothing_id });
      if (response.data) {
        clothingInfo = response.data;
      }
    } else if (targetData.clothing_id) {
      const response = await Api.getClothingInfo({ clothing_id: targetData.clothing_id });
      if (response.data) {
        clothingInfo = response.data;
      }
    } else if (targetData.sku) {
      // 根据SKU规则判断服装类型
      const sku = targetData.sku;
      isOemClothing = sku && sku.includes('_') && sku.split('_')[0].toLowerCase() === 'oem';

      if (isOemClothing) {
        // 获取OEM服装详情
        const oemClothingId = sku.split('_')[1];
        const response = await Api.getOemClothingInfo({ oem_clothing_id: oemClothingId });
        if (response.data) {
          clothingInfo = response.data;
        }
      } else {
        // 获取本厂服装详情
        const clothingId = targetData.clothing_id || (sku.includes('_') ? sku.split('_')[1] : sku);
        const response = await Api.getClothingInfo({ clothing_id: clothingId });
        if (response.data) {
          clothingInfo = response.data;
        }
      }
    } else if (targetData.classification_code) {
      // 最后的尝试：使用classification_code作为ID
      try {
        // 先尝试作为本厂服装ID
        const response = await Api.getClothingInfo({ clothing_id: targetData.classification_code });
        if (response.data) {
          clothingInfo = response.data;
          isOemClothing = false;
        }
      } catch (error) {
        try {
          // 如果本厂服装API失败，尝试OEM服装API
          const oemResponse = await Api.getOemClothingInfo({ oem_clothing_id: targetData.classification_code });
          if (oemResponse.data) {
            clothingInfo = oemResponse.data;
            isOemClothing = true;
          }
        } catch (oemError) {
          console.error('所有API尝试都失败了:', oemError.message);
        }
      }
    }

    return {
      data: clothingInfo,
      isOem: isOemClothing
    };
  },

  /**
   * 关闭服装信息卡片
   */
  onCloseClothingCard() {
    this.setData({
      showClothingCard: false,
      selectedClothingInfo: null,
      isOemClothing: false
    });
  },



  /**
   * 处理swipe-cell点击事件
   */
  onSwipeCellClick(e) {
    const position = e.detail;
    // 如果点击的是外部区域，关闭所有打开的swipe-cell
    if (position === "outside" || position === "cell") {
      // swipe-cell组件会自动处理关闭逻辑
    }
  },

  /**
   * 删除日志（冲正）
   */
  onDeleteLog(e) {
    const logId = e.currentTarget.dataset.logId;
    const logItem = e.currentTarget.dataset.logItem;

    wx.showModal({
      title: "确认删除",
      content: `确定要删除这条${
        logItem.type === 'inbound' ? '入库' :
        logItem.type === 'outbound' ? '出库' :
        logItem.type === 'transfer_out' ? '移出' :
        logItem.type === 'transfer_in' ? '移入' :
        logItem.type === 'inventory_surplus' ? '盘盈' :
        logItem.type === 'inventory_deficit' ? '盘亏' :
        logItem.type
      }日志吗？此操作将进行冲正处理，不可撤销。`,
      confirmText: "确认删除",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          this.executeDeleteLog(logId);
        }
      },
    });
  },

  /**
   * 执行删除日志操作
   */
  async executeDeleteLog(logId) {
    try {
      wx.showLoading({
        title: "处理中...",
        mask: true,
      });

      const response = await Api.reverseOperationLog({
        log_id: logId,
        operator_name: "小程序用户", // 可以从用户信息中获取
      });

      if (response.data && response.data.code === 200) {
        wx.showToast({
          title: "删除成功",
          icon: "success",
        });

        // 重新加载日志数据
        this.loadLogs(true);
      } else {
        wx.showToast({
          title: response.data?.message || "删除失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("删除日志失败:", error);
      wx.showToast({
        title: "删除失败",
        icon: "none",
      });
    } finally {
      wx.hideLoading();
    }
  }
});
