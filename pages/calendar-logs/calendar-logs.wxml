<!-- pages/calendar-logs/calendar-logs.wxml -->
<view class="page-container">
  <!-- 日志查询区域 -->
  <view class="logs-section">
    <!-- 页面头部信息 -->
    <view class="page-info-header" wx:if="{{ pageSource === 'warehouse-detail' || pageSource === 'clothing-detail' }}">
      <view wx:if="{{ pageSource === 'warehouse-detail' }}" class="source-info-item">
        <view class="source-info-label">仓库：</view>
        <view wx:if="{{ loadingSourceInfo }}" class="source-info-loading">
          <van-loading type="spinner" size="16px" />
        </view>
        <view wx:else class="source-info-text">{{ warehouseName || '加载中...' }}</view>
      </view>
      <view wx:elif="{{ pageSource === 'clothing-detail' }}" class="source-info-item">
        <view class="source-info-label">服装：</view>
        <view wx:if="{{ loadingSourceInfo }}" class="source-info-loading">
          <van-loading type="spinner" size="16px" />
        </view>
        <view wx:else class="source-info-text">{{ clothingName || '加载中...' }}</view>
      </view>
    </view>

    <!-- 操作类型按钮组 -->
    <view class="operation-buttons-header">
      <view class="operation-buttons">
        <view
          class="operation-btn {{selectedOperationType === 'inbound' ? 'active' : ''}}"
          data-type="inbound"
          bind:tap="onOperationTypeChange">
          入库
        </view>
        <view
          class="operation-btn {{selectedOperationType === 'outbound' ? 'active' : ''}}"
          data-type="outbound"
          bind:tap="onOperationTypeChange">
          出库
        </view>
        <view
          class="operation-btn {{selectedOperationType === 'transfer' ? 'active' : ''}}"
          data-type="transfer"
          bind:tap="onOperationTypeChange">
          移库
        </view>
        <view
          class="operation-btn {{selectedOperationType === 'inventory' ? 'active' : ''}}"
          data-type="inventory"
          bind:tap="onOperationTypeChange">
          盘存
        </view>
      </view>
    </view>

    <!-- 数据展示选项卡 -->
    <view class="data-tabs-header">
      <view class="data-tabs">
        <view
          class="data-tab {{selectedDataTab === 'detail' ? 'active' : ''}}"
          data-tab="detail"
          bind:tap="onDataTabChange">
          明细
        </view>
        <view
          class="data-tab {{selectedDataTab === 'warehouse' ? 'active' : ''}}"
          data-tab="warehouse"
          bind:tap="onDataTabChange">
          按仓库汇总
        </view>
        <view
          class="data-tab {{selectedDataTab === 'clothing' ? 'active' : ''}}"
          data-tab="clothing"
          bind:tap="onDataTabChange">
          按服装汇总
        </view>
      </view>
    </view>

    <!-- 日志列表 -->
    <scroll-view class="logs-list" scroll-y bindscrolltolower="loadMoreLogs" lower-threshold="100">
      <!-- 明细视图 -->
      <view wx:if="{{selectedDataTab === 'detail'}}">
        <view wx:if="{{logsList.length === 0 && !loadingLogs}}" class="empty-state">
          <van-empty description="暂无日志数据" />
        </view>
        <view wx:else>
          <view wx:for="{{logsList}}" wx:key="index" class="log-item">
          <!-- 使用van-swipe-cell实现右滑删除 -->
          <van-swipe-cell right-width="{{ 70 }}" name="{{item.id}}" bind:click="onSwipeCellClick">
            <!-- 紧凑的单行布局 -->
            <view class="log-item-content-compact">
              <!-- 序号列 -->
              <view class="serial-number">{{ index + 1 }}</view>
              <!-- 左侧：服装信息和数量 -->
              <view class="left-section">
                <!-- 服装名称和数量信息 -->
                <view wx:if="{{ item.contents_changes && item.contents_changes.length > 0 }}">
                  <view wx:for="{{ item.contents_changes }}" wx:for-item="change" wx:for-index="changeIndex" wx:key="changeIndex" class="clothing-info-compact">
                    <view wx:if="{{ pageSource !== 'clothing-detail' }}" class="clothing-name-compact clickable" data-item="{{item}}" data-change="{{change}}" bindtap="onClothingNameTap">
                      {{ change.product_name || change.clothing_name || '未知服装' }}
                    </view>
                    <view class="quantity-compact">
                      {{ change.quantity_change > 0 ? change.quantity_change : -change.quantity_change }}件
                      <view wx:if="{{ change.package_change }}" class="package-compact">
                        ({{ change.package_change > 0 ? change.package_change : -change.package_change }}包)
                      </view>
                    </view>
                  </view>
                </view>
                <view wx:else class="clothing-info-compact">
                  <view wx:if="{{ pageSource !== 'clothing-detail' }}" class="clothing-name-compact clickable" data-item="{{item}}" bindtap="onClothingNameTap">
                    {{ item.product_name || item.clothing_name || '未知服装' }}
                  </view>
                  <view class="quantity-compact">
                    {{ item.quantity_change > 0 ? item.quantity_change : -item.quantity_change }}件
                    <view wx:if="{{ item.outbound_package_count }}" class="package-compact">
                      ({{ item.outbound_package_count }}包)
                    </view>
                    <view wx:elif="{{ item.package_change }}" class="package-compact">
                      ({{ item.package_change > 0 ? item.package_change : -item.package_change }}包)
                    </view>
                  </view>
                </view>
              </view>
              <!-- 中间：仓库信息 -->
              <view wx:if="{{ pageSource !== 'warehouse-detail' }}" class="middle-section">
                <view wx:if="{{ item.type === 'transfer_out' || item.type === 'transfer_in' }}" class="warehouse-compact">
                  {{ item.type === 'transfer_out' ? '移出至:' : '移入自:' }}{{ item.transfer_notes || item.target_warehouse_name || item.source_warehouse_name || '--' }}
                </view>
                <view wx:else class="warehouse-compact">{{ item.warehouse_name || '--' }}</view>
              </view>
              <!-- 右侧：日期和操作标签 -->
              <view class="right-section">
                <view class="date-compact">{{ item.date }}</view>
                <van-tag color="{{ item.type === 'inbound' ? '#5698c3' : item.type === 'outbound' ? '#2c9678' : (item.type === 'transfer_out' || item.type === 'transfer_in') ? '#ddc871' : '#806d9e' }}" size="small">
                  {{ item.type === 'inbound' ? '入库' : item.type === 'outbound' ? '出库' : item.type === 'transfer_out'? '移出' :item.type === 'transfer_in' ? '移入' : item.type === 'inventory_surplus' ? '盘盈' : item.type === 'inventory_deficit' ? '盘亏' : item.type }}
                </van-tag>
              </view>
            </view>
            <!-- 右滑删除按钮 -->
            <view slot="right" class="delete-button" data-log-id="{{item.id}}" data-log-item="{{item}}" bindtap="onDeleteLog">
              删除
            </view>
          </van-swipe-cell>
        </view>
        <view class="bottom-text">到底了...</view>
        </view>
        <view wx:if="{{loadingLogs}}" class="loading-more">
          <van-loading type="spinner" size="20px" />
          <view>加载中...</view>
        </view>
      </view>

      <!-- 按仓库汇总视图 -->
      <view wx:elif="{{selectedDataTab === 'warehouse'}}">
        <view wx:if="{{warehouseSummaryList.length === 0 && !loadingLogs}}" class="empty-state">
          <van-empty description="暂无汇总数据" />
        </view>
        <view wx:else>
          <view wx:for="{{warehouseSummaryList}}" wx:key="warehouse_name" class="warehouse-compact-item">
            <!-- 仓库标题 - 简约设计 -->
            <view class="warehouse-compact-header">
              <view class="warehouse-serial">{{ index + 1 }}</view>
              <view class="warehouse-info">
                <view class="warehouse-title">{{ item.warehouse_name || '未知仓库' }}</view>
                <view class="warehouse-packages">{{ item.total_packages }}包</view>
              </view>
            </view>
            <!-- 服装列表 - 紧凑布局 -->
            <view class="clothing-compact-list">
              <view wx:for="{{item.clothing_list}}" wx:for-item="clothing" wx:key="classification_code" class="clothing-compact-item">
                <view class="clothing-compact-content">
                  <!-- 左列：服装名称 -->
                  <view class="clothing-compact-name clickable" data-item="{{clothing}}" bindtap="onSummaryClothingNameTap">
                    {{ clothing.clothing_name || '未知服装' }}
                  </view>
                  <!-- 中列：包裹数 -->
                  <view class="clothing-compact-packages">
                    <text wx:if="{{ clothing.total_packages && clothing.total_packages !== '0' }}" class="packages-text">{{ clothing.total_packages }}包</text>
                    <text wx:else class="packages-text">-</text>
                  </view>
                  <!-- 右列：件数 -->
                  <view class="clothing-compact-pieces">
                    <text class="pieces-text">{{ clothing.total_pieces }}件</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 按服装汇总视图 -->
      <view wx:elif="{{selectedDataTab === 'clothing'}}">
        <view wx:if="{{clothingSummaryList.length === 0 && !loadingLogs}}" class="empty-state">
          <van-empty description="暂无汇总数据" />
        </view>
        <view wx:else>
          <view wx:for="{{clothingSummaryList}}" wx:key="classification_code" class="clothing-simple-item">
            <view class="clothing-simple-content">
              <!-- 左列：序号 + 服装名称 -->
              <view class="clothing-simple-left">
                <view class="serial-number">{{ index + 1 }}</view>
                <view class="clothing-simple-name clickable" data-item="{{item}}" bindtap="onSummaryClothingNameTap">
                  {{ item.clothing_name || '未知服装' }}
                </view>
              </view>
              <!-- 中列：包裹数 -->
              <view class="clothing-simple-packages">
                <text wx:if="{{ item.total_packages && item.total_packages !== '0' }}" class="simple-packages-text">{{ item.total_packages }}包</text>
                <text wx:else class="simple-packages-text">-</text>
              </view>
              <!-- 右列：件数 -->
              <view class="clothing-simple-pieces">
                <text class="simple-pieces-text">{{ item.total_pieces }}件</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
  <!-- 服装信息卡片弹窗 -->
  <van-popup show="{{ showClothingCard }}" position="center" round bind:close="onCloseClothingCard" custom-style="width: 90%; max-width: 400px;">
    <view class="clothing-card-popup">
      <view class="popup-content">
        <clothing-info-card wx:if="{{ selectedClothingInfo }}" clothingInfo="{{ isOemClothing ? null : selectedClothingInfo }}" oemClothingInfo="{{ isOemClothing ? selectedClothingInfo : null }}" isOem="{{ isOemClothing }}" showDetailInfo="{{ true }}" disableNavigation="{{ false }}" />
      </view>
    </view>
  </van-popup>
</view>