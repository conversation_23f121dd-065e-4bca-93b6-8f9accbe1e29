#!/usr/bin/env ts-node

/**
 * 数据库迁移脚本：更新 transportation 集合中的 inbound_status 字段
 * 
 * 功能：
 * - 查找 date_arrived 字段存在且有值（不为 null 或 undefined）的文档
 * - 且 inbound_status 字段不等于 'partial_inbound' 的文档
 * - 将符合条件的文档的 inbound_status 字段更新为 'full_inbound'
 * 
 * 使用方法：
 * 1. pnpm run migrate:transportation-inbound-status
 * 2. npx ts-node scripts/update-transportation-inbound-status.ts
 */

import { connect, disconnect, Model } from 'mongoose'
import { config } from 'dotenv'
import { TransportationSchema, Transportation } from '../src/models/transportation.model'

// 加载环境变量
config()

// 日志工具函数
const log = {
  info: (message: string) => console.log(`[${new Date().toISOString()}] [INFO] ${message}`),
  success: (message: string) => console.log(`[${new Date().toISOString()}] [SUCCESS] ${message}`),
  error: (message: string) => console.error(`[${new Date().toISOString()}] [ERROR] ${message}`),
  warn: (message: string) => console.warn(`[${new Date().toISOString()}] [WARN] ${message}`)
}

// 迁移统计信息接口
interface MigrationStats {
  startTime: Date
  endTime?: Date
  totalDocuments: number
  matchedDocuments: number
  updatedDocuments: number
  successRate: number
  duration: number
}

// 数据验证结果接口
interface ValidationResult {
  totalCount: number
  dateArrivedCount: number
  eligibleCount: number
  alreadyFullInboundCount: number
}

/**
 * 数据验证函数
 */
async function validateData(transportationModel: Model<Transportation>): Promise<ValidationResult> {
  log.info('开始数据验证...')

  // 1. 统计总文档数
  const totalCount = await transportationModel.countDocuments({}).exec()
  log.info(`transportation 集合共有 ${totalCount} 条记录`)

  // 2. 统计有 date_arrived 字段的文档数量
  const dateArrivedCount = await transportationModel.countDocuments({
    date_arrived: { $exists: true, $ne: null }
  }).exec()
  log.info(`其中有 date_arrived 字段的文档数量: ${dateArrivedCount}`)

  // 3. 统计符合更新条件的文档数量
  const eligibleCount = await transportationModel.countDocuments({
    date_arrived: { $exists: true, $ne: null },
    inbound_status: { $ne: 'partial_inbound' }
  }).exec()
  log.info(`符合更新条件的文档数量: ${eligibleCount}`)

  // 4. 统计已经是 full_inbound 状态的文档数量
  const alreadyFullInboundCount = await transportationModel.countDocuments({
    date_arrived: { $exists: true, $ne: null },
    inbound_status: 'full_inbound'
  }).exec()
  log.info(`已经是 'full_inbound' 状态的文档数量: ${alreadyFullInboundCount}`)

  return {
    totalCount,
    dateArrivedCount,
    eligibleCount,
    alreadyFullInboundCount
  }
}

/**
 * 执行迁移操作
 */
async function executeMigration(transportationModel: Model<Transportation>): Promise<number> {
  log.info('开始执行迁移...')

  // 构建查询条件
  const filter = {
    date_arrived: { $exists: true, $ne: null },
    inbound_status: { $ne: 'partial_inbound' }
  }

  // 构建更新操作
  const update = {
    $set: { inbound_status: 'full_inbound' }
  }

  // 执行批量更新
  const result = await transportationModel.updateMany(filter, update).exec()
  
  log.info(`准备更新 ${result.matchedCount} 条记录...`)
  log.success(`迁移完成！更新了 ${result.modifiedCount} 条记录`)

  return result.modifiedCount
}

/**
 * 完整性检查
 */
async function integrityCheck(transportationModel: Model<Transportation>): Promise<boolean> {
  log.info('开始完整性检查...')

  // 检查更新后的状态
  const fullInboundCount = await transportationModel.countDocuments({
    date_arrived: { $exists: true, $ne: null },
    inbound_status: 'full_inbound'
  }).exec()

  // 检查是否还有符合条件但未更新的文档
  const remainingCount = await transportationModel.countDocuments({
    date_arrived: { $exists: true, $ne: null },
    inbound_status: { $ne: 'partial_inbound', $ne: 'full_inbound' }
  }).exec()

  if (remainingCount > 0) {
    log.warn(`发现 ${remainingCount} 条文档可能未正确更新`)
    return false
  }

  log.success(`完整性检查通过，共有 ${fullInboundCount} 条文档的 inbound_status 为 'full_inbound'`)
  return true
}

/**
 * 生成迁移报告
 */
function generateReport(stats: MigrationStats): void {
  console.log('\n============================================================')
  console.log('迁移报告')
  console.log('============================================================')
  console.log(`开始时间: ${stats.startTime.toISOString()}`)
  console.log(`结束时间: ${stats.endTime?.toISOString()}`)
  console.log(`执行时长: ${stats.duration.toFixed(2)} 秒`)
  console.log(`总文档数: ${stats.totalDocuments}`)
  console.log(`匹配文档数: ${stats.matchedDocuments}`)
  console.log(`更新文档数: ${stats.updatedDocuments}`)
  console.log(`更新成功率: ${stats.successRate.toFixed(2)}%`)
  console.log('============================================================')
}

/**
 * 主执行函数
 */
async function main(): Promise<void> {
  const stats: MigrationStats = {
    startTime: new Date(),
    totalDocuments: 0,
    matchedDocuments: 0,
    updatedDocuments: 0,
    successRate: 0,
    duration: 0
  }

  try {
    // 1. 连接数据库
    const databaseUrl = process.env.DATABASE_URL || 'mongodb://localhost:27017/JYNewData'
    log.info(`连接数据库: ${databaseUrl}`)
    
    await connect(databaseUrl)
    log.success('数据库连接成功')

    // 2. 创建模型
    const transportationModel = new Model<Transportation>('Transportation', TransportationSchema)

    // 3. 数据验证
    const validation = await validateData(transportationModel)
    stats.totalDocuments = validation.totalCount
    stats.matchedDocuments = validation.eligibleCount

    // 4. 执行迁移
    if (validation.eligibleCount === 0) {
      log.info('没有需要更新的文档，迁移结束')
      stats.updatedDocuments = 0
      stats.successRate = 100
    } else {
      stats.updatedDocuments = await executeMigration(transportationModel)
      stats.successRate = stats.matchedDocuments > 0 
        ? (stats.updatedDocuments / stats.matchedDocuments) * 100 
        : 100
    }

    // 5. 完整性检查
    const integrityPassed = await integrityCheck(transportationModel)
    if (!integrityPassed) {
      throw new Error('完整性检查失败')
    }

    // 6. 计算执行时间
    stats.endTime = new Date()
    stats.duration = (stats.endTime.getTime() - stats.startTime.getTime()) / 1000

    // 7. 生成报告
    generateReport(stats)

    log.success('迁移成功完成！')

  } catch (error) {
    log.error(`迁移失败: ${error instanceof Error ? error.message : String(error)}`)
    
    // 如果有详细错误信息，记录到控制台
    if (error instanceof Error && error.stack) {
      console.error('错误堆栈:', error.stack)
    }
    
    process.exit(1)
  } finally {
    // 断开数据库连接
    try {
      await disconnect()
      log.info('数据库连接已断开')
    } catch (disconnectError) {
      log.error(`断开数据库连接时出错: ${disconnectError}`)
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })
}

// 导出主函数供测试使用
export { main }
