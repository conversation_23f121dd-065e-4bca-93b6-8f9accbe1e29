# 数据库迁移脚本

本目录包含用于更新MongoDB数据库的迁移脚本。

## 脚本列表

### update-transportation-inbound-status.ts

**功能**: 更新 transportation 集合中的 inbound_status 字段

**目的**: 
- 查找 `date_arrived` 字段存在且有值（不为 null 或 undefined）的文档
- 且 `inbound_status` 字段不等于 'partial_inbound' 的文档
- 将符合条件的文档的 `inbound_status` 字段更新为 'full_inbound'

**使用方法**:

1. **使用 npm/pnpm 脚本运行**:
   ```bash
   pnpm run migrate:transportation-inbound-status
   ```

2. **直接使用 ts-node 运行**:
   ```bash
   npx ts-node scripts/update-transportation-inbound-status.ts
   ```

**环境变量配置**:

脚本会自动加载 `.env` 文件中的环境变量。确保设置以下变量：

```env
DATABASE_URL=mongodb://localhost:27017/JYNewData
```

如果未设置 `DATABASE_URL`，脚本将使用默认值：`mongodb://localhost:27017/JYNewData`

**脚本特性**:

1. **数据验证**: 执行前检查数据库连接和集合状态
2. **完整性检查**: 执行后验证更新结果
3. **详细日志**: 提供执行过程的详细日志记录
4. **错误处理**: 包含完善的错误处理机制
5. **统计报告**: 生成详细的迁移统计报告

**输出示例**:

```
[2025-01-25T10:30:00.000Z] [INFO] 连接数据库: mongodb://localhost:27017/JYNewData
[2025-01-25T10:30:01.000Z] [SUCCESS] 数据库连接成功
[2025-01-25T10:30:01.100Z] [INFO] 开始数据验证...
[2025-01-25T10:30:01.200Z] [INFO] transportation 集合共有 150 条记录
[2025-01-25T10:30:01.300Z] [INFO] 其中有 date_arrived 字段的文档数量: 80
[2025-01-25T10:30:01.400Z] [INFO] 符合更新条件的文档数量: 75
[2025-01-25T10:30:01.500Z] [INFO] 开始执行迁移...
[2025-01-25T10:30:01.600Z] [INFO] 准备更新 75 条记录...
[2025-01-25T10:30:02.000Z] [SUCCESS] 迁移完成！更新了 75 条记录
[2025-01-25T10:30:02.100Z] [INFO] 开始完整性检查...
[2025-01-25T10:30:02.200Z] [SUCCESS] 完整性检查通过，共有 75 条文档的 inbound_status 为 'full_inbound'
[2025-01-25T10:30:02.300Z] [SUCCESS] 迁移成功完成！
============================================================
迁移报告
============================================================
开始时间: 2025-01-25T10:30:00.000Z
结束时间: 2025-01-25T10:30:02.300Z
执行时长: 2.30 秒
总文档数: 150
匹配文档数: 75
更新文档数: 75
更新成功率: 100.00%
============================================================
[2025-01-25T10:30:02.400Z] [INFO] 数据库连接已断开
```

**注意事项**:

1. **备份数据**: 在运行迁移脚本前，建议备份相关数据
2. **测试环境**: 建议先在测试环境中运行脚本
3. **权限检查**: 确保数据库用户有足够的权限执行更新操作
4. **网络连接**: 确保能够正常连接到MongoDB数据库

**故障排除**:

1. **连接失败**: 检查 `DATABASE_URL` 是否正确，数据库是否运行
2. **权限错误**: 确保数据库用户有读写权限
3. **集合不存在**: 确保 `transportation` 集合存在
4. **依赖缺失**: 运行 `pnpm install` 安装所需依赖

## 开发指南

### 创建新的迁移脚本

1. 在 `scripts` 目录下创建新的 TypeScript 文件
2. 遵循现有脚本的结构和模式
3. 包含适当的错误处理和日志记录
4. 在 `package.json` 中添加相应的脚本命令
5. 更新此 README 文件

### 脚本结构建议

```typescript
#!/usr/bin/env ts-node

import { connect, disconnect } from 'mongoose'
import { config } from 'dotenv'

// 加载环境变量
config()

// 主执行函数
async function main(): Promise<void> {
  try {
    // 连接数据库
    await connect(process.env.DATABASE_URL || 'mongodb://localhost:27017/JYNewData')
    
    // 执行迁移逻辑
    
    // 生成报告
    
  } catch (error) {
    console.error('迁移失败:', error)
    process.exit(1)
  } finally {
    // 断开数据库连接
    await disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch((error) => {
    console.error('脚本执行失败:', error)
    process.exit(1)
  })
}
```
