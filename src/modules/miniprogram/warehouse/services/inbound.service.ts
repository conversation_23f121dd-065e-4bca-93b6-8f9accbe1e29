import { Injectable, HttpException, HttpStatus } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { Product } from '../../../../models/product.model'
import { Package } from '../../../../models/package.model'
import { OperationLog } from '../../../../models/operationLog.model'
import { Warehouse } from '../../../../models/warehouse.model'
import { Clothing } from '../../../../models/clothing.model'
import { OemClothing } from '../../../../models/oemClothing.model'
import { Transportation } from '../../../../models/transportation.model'

@Injectable()
export class InboundService {
  constructor(
    @InjectModel('Product') private productModel: Model<Product>,
    @InjectModel('Package') private packageModel: Model<Package>,
    @InjectModel('OperationLog') private operationLogModel: Model<OperationLog>,
    @InjectModel('Warehouse') private warehouseModel: Model<Warehouse>,
    @InjectModel('Clothing') private clothingModel: Model<Clothing>,
    @InjectModel('OemClothing') private oemClothingModel: Model<OemClothing>,
    @InjectModel('Transportation') private transportationModel: Model<Transportation>
  ) {}

  /**
   * 生成包裹分类码
   * @param contents 包裹内容数组
   * @returns 分类码字符串
   */
  private generateClassificationCode(contents: Array<{ sku: string; original_quantity: number }>): string {
    // 按SKU排序确保分类码的一致性
    const sortedContents = contents
      .map(content => `${content.sku}_${content.original_quantity}`)
      .sort()

    return sortedContents.join('_')
  }

  /**
   * 查找或创建产品
   */
  private async findOrCreateProduct(item: any): Promise<Product> {
    // 先尝试查找现有产品
    let product = null
    
    if (item.oem === '是' && item.clothing_id) {
      product = await this.productModel.findOne({ oem_clothing_id: item.clothing_id })
    } else if (item.clothing_id) {
      product = await this.productModel.findOne({ clothing_id: item.clothing_id })
    }

    // 如果找不到，创建新产品
    if (!product) {
      const sku = item.oem === '是' ? `OEM_${item.clothing_id}` : `CLO_${item.clothing_id}`
      
      product = await this.productModel.create({
        sku,
        name: item.clothing_name || '未知产品',
        description: '',
        unit: '件',
        specs: {},
        clothing_id: item.oem === '是' ? undefined : item.clothing_id,
        oem_clothing_id: item.oem === '是' ? item.clothing_id : undefined,
        is_oem: item.oem === '是',
        images: [],
        status: 'active'
      })
    }

    return product
  }

  /**
   * 记录操作日志
   */
  private async logOperation(logData: any, operationDate?: string) {
    const timestamp = operationDate ? new Date(operationDate) : new Date()
    await this.operationLogModel.create({
      ...logData,
      timestamp
    })
  }

  /**
   * 更新服装库存数据
   */
  private async updateClothingStock(
    operationType: 'inbound' | 'outbound' | 'transfer' | 'inventory_gain' | 'inventory_loss',
    clothingUpdates: Array<{
      clothing_id?: string
      oem_clothing_id?: string
      quantity: number
    }>
  ) {
    try {
      for (const update of clothingUpdates) {
        if (update.clothing_id) {
          // 普通服装更新
          if (operationType === 'inbound' || operationType === 'inventory_gain') {
            // 入库或盘盈：增加到货数和库存数
            await this.clothingModel.updateOne(
              { clothing_id: update.clothing_id },
              {
                $inc: {
                  arrival_quantity: operationType === 'inbound' ? update.quantity : 0,
                  stock_quantity: update.quantity
                }
              }
            )
          } else if (operationType === 'outbound' || operationType === 'inventory_loss') {
            // 出库或盘亏：减少库存数
            await this.clothingModel.updateOne(
              { clothing_id: update.clothing_id },
              {
                $inc: {
                  stock_quantity: -update.quantity
                }
              }
            )
          }
        }

        if (update.oem_clothing_id) {
          // OEM服装更新
          if (operationType === 'inbound' || operationType === 'inventory_gain') {
            // 入库或盘盈：增加到货数和库存数
            await this.oemClothingModel.updateOne(
              { oem_clothing_id: update.oem_clothing_id },
              {
                $inc: {
                  arrival_quantity: operationType === 'inbound' ? update.quantity : 0,
                  stock_quantity: update.quantity
                }
              }
            )
          } else if (operationType === 'outbound' || operationType === 'inventory_loss') {
            // 出库或盘亏：减少库存数
            await this.oemClothingModel.updateOne(
              { oem_clothing_id: update.oem_clothing_id },
              {
                $inc: {
                  stock_quantity: -update.quantity
                }
              }
            )
          }
        }
      }
    } catch (error) {
      console.error('更新服装库存失败:', error)
      throw error
    }
  }

  /**
   * 入库操作 - 根据新的包裹为中心的设计
   */
  async inbound(inboundData: {
    transportation_id: string
    inbound_items: Array<{
      warehouse_id: string
      clothing_id: string
      clothing_name: string
      oem: string
      out_pcs: number
      pieces_per_package: number
      inbound_pcs: number
      package_quantity: number
      supplier?: string
      transportation_id: string
      series_number: number
    }>
    operator: string
    operation_date?: string
  }) {
    try {
      // 生成入库批次号
      const batchCode = `BATCH-${new Date().toISOString().split('T')[0].replace(/-/g, '')}-${Date.now()}`
      const createdPackages = []

      // 按transportation_id + series_number + warehouse_id分组处理入库项
      // 这样确保不同仓库的相同系列号会分别处理
      const groupedItems = new Map<string, typeof inboundData.inbound_items>()

      for (const item of inboundData.inbound_items) {
        const groupKey = `${item.transportation_id}_${item.series_number}_${item.warehouse_id}`
        if (!groupedItems.has(groupKey)) {
          groupedItems.set(groupKey, [])
        }
        groupedItems.get(groupKey)!.push(item)
      }

      // 为每个分组创建包裹
      for (const [groupKey, items] of groupedItems) {
        const firstItem = items[0]
        const packageQuantity = firstItem.package_quantity

        // 用于存储当前分组创建的包裹，避免日志重复
        const currentGroupCreatedPackages = []

        console.log(`处理分组: ${groupKey}`)
        console.log(`- 仓库ID: ${firstItem.warehouse_id}`)
        console.log(`- 货运单ID: ${firstItem.transportation_id}`)
        console.log(`- 系列号: ${firstItem.series_number}`)
        console.log(`- 包裹数量: ${packageQuantity}`)
        console.log(`- 包含的服装: ${items.map(item => `${item.clothing_name}(${item.clothing_id})`).join(', ')}`)

        // 检查已存在的包裹 - 需要同时匹配仓库ID
        const existingPackages = await this.packageModel.find({
          transportation_id: firstItem.transportation_id,
          series_number: firstItem.series_number,
          warehouse_id: firstItem.warehouse_id,
          status: { $in: ['in_stock', 'partially_shipped'] }
        }).exec()

        const existingPackageCount = existingPackages.length
        console.log(`- 已存在包裹数: ${existingPackageCount}`)

        // 为每个包裹创建一个Package文档（一个物理包裹）
        console.log(`开始创建 ${packageQuantity} 个包裹，已存在 ${existingPackageCount} 个`)

        // 查找该transportation_id + series_number下所有已存在的包裹（不限仓库）
        // 以确定下一个可用的包裹编号
        const allExistingPackages = await this.packageModel.find({
          transportation_id: firstItem.transportation_id,
          series_number: firstItem.series_number
        }).exec()

        // 提取所有已存在的包裹编号，找出最大编号
        const existingNumbers = allExistingPackages
          .map(pkg => {
            const match = pkg.package_code.match(/-(\d+)$/)
            return match ? parseInt(match[1]) : 0
          })
          .filter(num => !isNaN(num))

        const maxExistingNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) : 0
        console.log(`该系列号下最大包裹编号: ${maxExistingNumber}`)

        for (let packageIndex = 0; packageIndex < packageQuantity; packageIndex++) {
          // 从最大编号+1开始分配新的包裹编号
          const actualPackageIndex = maxExistingNumber + packageIndex + 1
          const packageCode = `${firstItem.transportation_id}-${firstItem.series_number}-${actualPackageIndex}`
          console.log(`准备创建包裹: ${packageCode}`)

          // 检查包裹是否已存在（双重保险）
          const existingPackage = await this.packageModel.findOne({ package_code: packageCode }).exec()
          if (existingPackage) {
            console.log(`包裹 ${packageCode} 已存在，跳过`)
            continue
          }

          // 创建包裹内容 - 根据包裹类型正确计算数量
          const contents = []

          // 判断是否为混合包裹（同一个transportation_id + series_number下有多个不同的clothing_id）
          const uniqueClothingIds = new Set(items.map(item => item.clothing_id))
          const isMixedPackage = uniqueClothingIds.size > 1
          console.log(`包裹类型: ${isMixedPackage ? '混合包裹' : '单一服装包裹'}，包含服装: ${Array.from(uniqueClothingIds).join(', ')}`)

          for (const item of items) {
            // 查找或创建产品
            const product = await this.findOrCreateProduct(item)

            // 计算该服装在单个包裹中的数量
            let currentQuantity
            if (isMixedPackage) {
              // 混合包裹：每个包裹包含该服装的实际件数除以包裹数量
              currentQuantity = Math.ceil(item.inbound_pcs / packageQuantity)
            } else {
              // 单一服装包裹：每个包裹包含pieces_per_package件数
              currentQuantity = item.pieces_per_package || Math.ceil(item.inbound_pcs / packageQuantity)
            }

            console.log(`服装 ${item.clothing_name}: 总件数=${item.inbound_pcs}, 包裹数=${packageQuantity}, 每包件数=${currentQuantity}`)

            contents.push({
              product_id: product._id,
              sku: product.sku,
              name: product.name,
              original_quantity: currentQuantity,
              current_quantity: currentQuantity,
              clothing_id: item.oem === '是' ? undefined : item.clothing_id,
              oem_clothing_id: item.oem === '是' ? item.clothing_id : undefined,
              is_oem: item.oem === '是'
            })
          }

          // 生成包裹分类码
          const classificationCode = this.generateClassificationCode(contents)

          // 确定包裹类型
          const packageType = isMixedPackage ? 'mixed' : 'single'

          // 修复：使用用户选择的入库日期而不是当前时间
          const inboundDate = inboundData.operation_date ? new Date(inboundData.operation_date) : new Date()

          const packageData = {
            package_code: packageCode,
            classification_code: classificationCode,
            package_type: packageType,
            inbound_batch_code: batchCode,
            warehouse_id: firstItem.warehouse_id,
            location_code: 'A-01-F1-R1', // 默认位置
            status: 'in_stock',
            remaining_percentage: 1.0, // 新入库的包裹剩余百分比为100%
            contents: contents,
            transportation_id: firstItem.transportation_id,
            series_number: firstItem.series_number,
            supplier: firstItem.supplier || '',
            inbound_at: inboundDate, // 使用用户选择的入库日期
            last_updated_at: new Date() // 最后更新时间使用当前时间
          }

          try {
            const createdPackage = await this.packageModel.create(packageData)
            createdPackages.push(createdPackage)
            currentGroupCreatedPackages.push(createdPackage)
            console.log(`成功创建包裹: ${packageCode}，类型: ${packageType}，分类码: ${classificationCode}，内容: ${JSON.stringify(contents.map(c => ({sku: c.sku, name: c.name, quantity: c.original_quantity})))}`)

          } catch (error) {
            console.error(`创建包裹 ${packageCode} 失败:`, error)
            if ((error as any).code === 11000) {
              console.log(`包裹 ${packageCode} 重复，跳过`)
              continue
            } else {
              throw error
            }
          }
        }

        // 为当前分组中实际创建的包裹记录操作日志
        console.log(`当前分组创建了 ${currentGroupCreatedPackages.length} 个包裹，开始记录日志`)

        for (const createdPackage of currentGroupCreatedPackages) {
          // 从包裹内容中直接获取数量信息，避免重复计算
          const contentsChanges = createdPackage.contents.map((content: any) => ({
            sku: content.sku,
            product_name: content.name,
            quantity_change: content.original_quantity,
            before_quantity: 0,
            after_quantity: content.original_quantity
          }))

          await this.logOperation({
            operation_type: 'inbound',
            operator_name: inboundData.operator,
            warehouse_id: firstItem.warehouse_id,
            package_code: createdPackage.package_code,
            contents_changes: contentsChanges,
            details: {
              inbound_batch_code: batchCode,
              transportation_id: firstItem.transportation_id,
              series_number: firstItem.series_number,
              classification_code: createdPackage.classification_code,
              notes: `入库操作 - 货运单：${firstItem.transportation_id}，系列号：${firstItem.series_number}，包裹：${createdPackage.package_code}`
            }
          }, inboundData.operation_date)

          console.log(`已为包裹 ${createdPackage.package_code} 记录日志`)
        }
      }

      // 计算正确的请求包裹总数 - 按分组计算，避免重复计算
      const totalRequested = Array.from(groupedItems.values()).reduce((sum, items) => {
        return sum + items[0].package_quantity // 每组只计算一次package_quantity
      }, 0)

      // 更新服装库存数据（仅在成功创建包裹时）
      if (createdPackages.length > 0) {
        const clothingUpdates: Array<{
          clothing_id?: string
          oem_clothing_id?: string
          quantity: number
        }> = []

        // 统计每个服装的入库数量
        const clothingQuantityMap = new Map<string, number>()
        const oemClothingQuantityMap = new Map<string, number>()

        for (const pkg of createdPackages) {
          for (const content of pkg.contents) {
            if (content.clothing_id) {
              // 判断是否为OEM服装
              if (content.clothing_id.includes('_') && content.clothing_id.split('_')[0] === 'oem') {
                const currentQuantity = oemClothingQuantityMap.get(content.clothing_id) || 0
                oemClothingQuantityMap.set(content.clothing_id, currentQuantity + content.original_quantity)
              } else {
                const currentQuantity = clothingQuantityMap.get(content.clothing_id) || 0
                clothingQuantityMap.set(content.clothing_id, currentQuantity + content.original_quantity)
              }
            }
            if (content.oem_clothing_id) {
              const currentQuantity = oemClothingQuantityMap.get(content.oem_clothing_id) || 0
              oemClothingQuantityMap.set(content.oem_clothing_id, currentQuantity + content.original_quantity)
            }
          }
        }

        // 构建更新数据
        for (const [clothingId, quantity] of clothingQuantityMap) {
          clothingUpdates.push({ clothing_id: clothingId, quantity })
        }
        for (const [oemClothingId, quantity] of oemClothingQuantityMap) {
          clothingUpdates.push({ oem_clothing_id: oemClothingId, quantity })
        }

        // 执行库存更新
        if (clothingUpdates.length > 0) {
          await this.updateClothingStock('inbound', clothingUpdates)
        }
      }

      return {
        code: 200,
        data: {
          batch_code: batchCode,
          packages_created: createdPackages.length,
          packages: createdPackages,
          total_requested: totalRequested,
          actual_created: createdPackages.length
        },
        message: createdPackages.length > 0 ? '入库成功' : '所有包裹已存在，无需重复入库'
      }
    } catch (error) {
      console.error('入库操作失败:', error)
      throw new HttpException('入库操作失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取已入库统计数据
   */
  async getInboundStatistics(transportation_id: string) {
    try {
      // 通过packages集合统计已入库数据 - 修复：包含所有已入库状态
      const inboundStats = await this.packageModel.aggregate([
        {
          $match: {
            transportation_id: transportation_id,
            status: { $in: ['in_stock', 'partially_shipped', 'shipped'] }
          }
        },
        {
          $group: {
            _id: '$series_number',
            package_count: { $sum: 1 },
            total_pieces: { $sum: { $sum: '$contents.current_quantity' } }
          }
        },
        {
          $group: {
            _id: null,
            total_packages: { $sum: '$package_count' },
            total_pieces: { $sum: '$total_pieces' }
          }
        }
      ]).exec()

      const result = inboundStats[0] || { total_packages: 0, total_pieces: 0 }

      return {
        code: 200,
        data: {
          packages: result.total_packages,
          pieces: result.total_pieces
        },
        message: '获取已入库统计成功'
      }
    } catch (error: any) {
      console.error('获取已入库统计失败:', error)
      throw new HttpException(error.message || '获取已入库统计失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 获取每日入库汇总数据
   * 按日期汇总入库数量，用于日历组件显示入库状态指示器
   */
  async getDailyInboundSummary(params: {
    year: number
    month: number
    warehouse_id?: string
    clothing_id?: string
    oem_clothing_id?: string
  }) {
    try {
      const { year, month, warehouse_id, clothing_id, oem_clothing_id } = params

      // 计算查询的月份的起止日期
      const startDate = new Date(year, month - 1, 1) // 月份从0开始，所以要减1
      const endDate = new Date(year, month, 0) // 下个月的第0天就是当前月的最后一天

      // 设置时间为当天的开始和结束
      startDate.setHours(0, 0, 0, 0)
      endDate.setHours(23, 59, 59, 999)

      console.log(`查询${year}年${month}月的入库汇总数据，起始日期: ${startDate.toISOString()}, 结束日期: ${endDate.toISOString()}`)
      console.log('查询参数:', { warehouse_id, clothing_id, oem_clothing_id })

      // 构建查询条件
      const matchStage: any = {
        operation_type: 'inbound',
        timestamp: { $gte: startDate, $lte: endDate },
        is_reversal: { $ne: true },
        is_reversed: { $ne: true }
      }

      // 先查询总的入库记录数量
      const totalInboundLogs = await this.operationLogModel.countDocuments(matchStage)
      console.log(`匹配的入库日志总数: ${totalInboundLogs}`)

      // 如果指定了仓库ID
      if (warehouse_id) {
        matchStage.warehouse_id = warehouse_id
      }

      // 如果指定了服装ID或OEM服装ID
      if (clothing_id || oem_clothing_id) {
        const clothingConditions = []
        if (clothing_id) {
          clothingConditions.push({ 'contents_changes.clothing_id': clothing_id })
        }
        if (oem_clothing_id) {
          clothingConditions.push({ 'contents_changes.oem_clothing_id': oem_clothing_id })
        }
        matchStage.$or = clothingConditions
      }

      // 聚合查询每日入库数据 - 修复：入库操作没有outbound_package_count字段，应该按包裹计数
      const dailySummary = await this.operationLogModel.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: {
              year: { $year: '$timestamp' },
              month: { $month: '$timestamp' },
              day: { $dayOfMonth: '$timestamp' },
              package_code: '$package_code' // 按包裹编码分组，避免重复计算
            }
          }
        },
        {
          $group: {
            _id: {
              year: '$_id.year',
              month: '$_id.month',
              day: '$_id.day'
            },
            package_count: { $sum: 1 }, // 每个唯一包裹计为1个包裹
            operation_count: { $sum: 1 }
          }
        },
        {
          $project: {
            _id: 0,
            date: {
              $concat: [
                { $toString: '$_id.year' }, '-',
                { $toString: { $cond: [{ $lt: ['$_id.month', 10] }, { $concat: ['0', { $toString: '$_id.month' }] }, { $toString: '$_id.month' }] } }, '-',
                { $toString: { $cond: [{ $lt: ['$_id.day', 10] }, { $concat: ['0', { $toString: '$_id.day' }] }, { $toString: '$_id.day' }] } }
              ]
            },
            inbound_count: '$package_count', // 使用包裹数量
            operation_count: '$operation_count'
          }
        },
        { $sort: { date: 1 } }
      ])

      // 转换为前端需要的格式 {date: count}
      const result: Record<string, number> = {}
      dailySummary.forEach((item: any) => {
        result[item.date] = item.inbound_count
      })

      console.log(`入库汇总查询结果: 找到 ${dailySummary.length} 个日期的数据`)
      console.log('详细数据:', JSON.stringify(result, null, 2))

      return {
        code: 200,
        message: '获取每日入库汇总成功',
        data: result
      }
    } catch (error: any) {
      console.error('获取每日入库汇总失败:', error)
      throw new HttpException(error.message || '获取每日入库汇总失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 入库冲正操作
   */
  async reverseInboundOperation(reverseData: {
    package_code: string
    operator: string
    notes?: string
    operation_date?: string
  }): Promise<any> {
    try {
      // 查找包裹
      const packageDoc = await this.packageModel.findOne({
        package_code: reverseData.package_code
      })

      if (!packageDoc) {
        throw new HttpException(`包裹 ${reverseData.package_code} 不存在`, HttpStatus.BAD_REQUEST)
      }

      // 验证包裹状态
      if (packageDoc.status !== 'in_stock') {
        // 如果包裹不是in_stock状态，查找相同classification_code且status为in_stock的包裹
        const alternativePackage = await this.packageModel.findOne({
          classification_code: packageDoc.classification_code,
          warehouse_id: packageDoc.warehouse_id,
          status: 'in_stock'
        })

        if (!alternativePackage) {
          throw new HttpException(
            `包裹 ${reverseData.package_code} 状态为 ${packageDoc.status}，且没有找到相同分类码的可用包裹`,
            HttpStatus.BAD_REQUEST
          )
        }

        // 使用替代包裹执行冲正
        return this.reverseInboundOperation({
          ...reverseData,
          package_code: alternativePackage.package_code
        })
      }

      // 删除包裹记录
      await this.packageModel.deleteOne({ package_code: reverseData.package_code })

      // 记录冲正日志
      const contentsChanges = packageDoc.contents.map((content: any) => ({
        sku: content.sku,
        product_name: content.name,
        quantity_change: -content.original_quantity,
        before_quantity: content.original_quantity,
        after_quantity: 0
      }))

      await this.logOperation({
        operation_type: 'reversal',
        operator_name: reverseData.operator,
        warehouse_id: packageDoc.warehouse_id,
        package_code: reverseData.package_code,
        contents_changes: contentsChanges,
        details: {
          original_classification_code: packageDoc.classification_code,
          transportation_id: packageDoc.transportation_id,
          series_number: packageDoc.series_number,
          notes: reverseData.notes || '入库冲正操作'
        },
        is_reversal: true
      }, reverseData.operation_date)

      // 更新货运单状态为部分入库
      if (packageDoc.transportation_id) {
        await this.transportationModel.updateOne(
          { transportation_id: packageDoc.transportation_id },
          { $set: { inbound_status: 'partial' } }
        )
      }

      // 更新服装库存数据（减少库存）
      const clothingUpdates: Array<{
        clothing_id?: string
        oem_clothing_id?: string
        quantity: number
      }> = []

      for (const content of packageDoc.contents) {
        if (content.clothing_id) {
          clothingUpdates.push({
            clothing_id: content.clothing_id,
            quantity: content.original_quantity
          })
        }
        if (content.oem_clothing_id) {
          clothingUpdates.push({
            oem_clothing_id: content.oem_clothing_id,
            quantity: content.original_quantity
          })
        }
      }

      if (clothingUpdates.length > 0) {
        await this.updateClothingStock('outbound', clothingUpdates)
      }

      return {
        code: 200,
        data: {
          reversed_package: reverseData.package_code,
          transportation_id: packageDoc.transportation_id,
          affected_contents: contentsChanges
        },
        message: '入库冲正成功'
      }
    } catch (error: any) {
      console.error('入库冲正操作失败:', error)
      throw new HttpException(error.message || '入库冲正操作失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }
}
